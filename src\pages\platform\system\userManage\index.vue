<template>
    <div class="base-page">
        <div class="left">
            <select-material-class ref="materialClassRef" :productType="0"/>
        </div>
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <div class="left-btn dfa">
                            <el-button type="primary" @click="operate_td('新增')">新增</el-button>
                            <!-- <el-button @click="updateStateBatch(true)" class="btn-greenYellow">批量启用</el-button>
                            <el-button @click="updateStateBatch(false)" class="btn-delete">批量停用</el-button> -->
                        </div>
                    </div>
                    <div class="search_box" style="width: 400px">
                        <el-input
                            clearable type="text" @blur="getTableData" placeholder="请输入用户名、员工号或手机号"
                            v-model="init.keyword"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="getTableData" alt=""/>
                        </el-input>
                    </div>
                </div>
            </div>
            <!--表格-->
            <div class="e-table" style="width: 100%;">
                <el-table ref="mainTable"
                    v-loading="tableLoading" class="table" :height="rightTableHeight" :data="tableData" border
                    @selection-change="selectionChangeHandle" @row-click="handleCurrentInventoryClick">
                    <!-- <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column> -->
                    <el-table-column label="操作" width="230">
                        <template v-slot="scope">
                            <span class="action" @click="operate_td('编辑',scope.row,)">编辑</span>
                            <span class="action" @click="operate_td('删除',scope.row)">删除</span>
                            <span class="action" @click="operate_td('重置密码',scope.row)">重置密码</span>
                            <!-- <el-button
                                style="padding:0 8px;" v-if="scope.row.state==0"
                                size="mini" type="success"
                                @click="updateState(scope.row,true,'启用')"
                            >启用</el-button>
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.state==1"
                                size="mini" type="danger"
                                @click="updateState(scope.row,false,'停用')"
                            >停用</el-button> -->
                        </template>
                    </el-table-column>
                    <el-table-column label="用户名" width="" prop="userName"/>
                    <el-table-column label="姓名" width="" prop="name"/>
                    <el-table-column label="手机号" width="" prop="phone"/>
                    <el-table-column label="机构" width="" prop="institution"/>
                    <el-table-column label="角色" width="" prop="role"/>
                    <el-table-column label="状态" width="130">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.state==1" type="success">正常</el-tag>
                            <el-tag v-if="scope.row.state==0" type="danger">禁用</el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <Pagination
                v-show="tableData && tableData.length > 0"
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <el-dialog v-dialogDrag :title="userModal.title" :visible.sync="userModal.visible" width="40%" :close-on-click-modal="false">
            <div class="e-form" style="padding: 0 10px 10px;">
                <el-form ref="formEdit" :rules="formRules" :model="materialInfo" label-width="150px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="用户名称：" prop="userName">
                                <el-input v-if="userModal.title == '新增用户'" placeholder="请输入用户名称" v-model="materialInfo.userName"/>
                                <el-input v-else v-model="materialInfo.userName" disabled/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="密码：" prop="password">
                                <el-input v-if="userModal.title == '新增用户'" placeholder="请输入密码" v-model="materialInfo.password"/>
                                <el-input v-else type="password" v-model="materialInfo.password" disabled/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="姓名：" prop="name">
                                <el-input v-model="materialInfo.name" placeholder="请输入姓名"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="手机号：" prop="phone">
                                <el-input v-model="materialInfo.phone" placeholder="请输入手机号"/>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="角色：" prop="role">
                                <el-select v-model="materialInfo.role" placeholder="请选择角色">
                                    <el-option v-for="item in roleOptions" :key="item.value" :label="item.label"
                                        :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="状态：" prop="state">
                                <el-switch
                                    v-model="materialInfo.state"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949"
                                    active-text="正常"
                                    inactive-text="禁用"
                                    :active-value="1"
                                    :inactive-value="0"
                                >
                                </el-switch>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="buttons">
                        <el-button type="success" v-show="userModal.title == '新增用户'"  @click="onSave()">保存</el-button>
                        <el-button type="success" v-show="userModal.title == '编辑用户'" @click="updateM()">修改</el-button>
                        <el-button type="warning" v-show="userModal.title == '新增用户'" @click="onSave(true)">保存并下一个</el-button>
                        <el-button type="fail" @click="closeViewListM()">取消</el-button>
                    </div>
                </el-form>
            </div>
        </el-dialog>
        <el-dialog v-dialogDrag title="重置密码" :visible.sync="passwordModal.visible" width="40%" :close-on-click-modal="false">
            <div class="e-form" style="padding: 0 10px 10px;">
                <el-form ref="passwordRef" :rules="passwordRules" :model="passwordForm" label-width="150px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="密码：" prop="password">
                                <el-input placeholder="请输入密码" v-model="passwordForm.password" :type="pwType" >
                                    <i slot="suffix"><img class="input-icon pointer" :src="hidePass" @click="toggleShowPass" alt=""/></i>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div class="buttons">
                        <el-button type="success" @click="confirm_password()">保存</el-button>
                        <el-button type="fail" @click="passwordModal.visible = false">取消</el-button>
                    </div>
                </el-form>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from './selectTree'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapState } from 'vuex'
import {
    batchUpdateMaterialDtlState,
    queryPageMaterialDtl,
} from '@/api/platform/product/materialManage'

import ico_hide from '@/assets/images/ico_hide.png'
import ico_show from '@/assets/images/ico_show.png'
export default {
    components: { SelectMaterialClass, Pagination },
    watch: {},
    computed: {
        ...mapState(['userInfo']),
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        }
    },
    data () {
        return {
            materialInfo: {
                userName: '',
                password: '',
                name: '',
                state: 1,
                role: undefined,
                phone: '',
                classId: '',
                className: '',
                classPath: [],
            },
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                classId: null,
                keyword: '',
                classPath: [],
                className: null
            },
            dataListSelections: [], //表格选中的数据
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            screenHeight: 0,
            formRules: {
                userName: [{ required: true, message: '请输入用户名', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' }],
                password: [{ required: true, message: '请输入密码', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' }],
                name: [{ required: true, message: '请输入姓名', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' }],
                phone: [{ required: true, message: '请输入手机号', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' }],
                role: [{ required: true, message: '请选择角色', trigger: 'blur' }],
                state: [{ required: true, message: '请选择状态', trigger: 'blur' }],
            },
            userModal: {
                visible: false, title: '',
            },
            roleOptions: [],
            passwordForm: {
                password: '',
            },
            passwordRules: {
                password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
            },
            passwordModal: {
                visible: false, userId: '',
            },
            ico_hide,
            ico_show,
            hidePass: ico_hide,
            pwType: 'password',
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        this.getTableData()
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        let res = [
            { value: '1', label: '管理员1' },
            { value: '2', label: '管理员2' }
        ]
        this.roleOptions = res
        this.orgId = this.userInfo.orgInfo.orgId
    },
    methods: {
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.init.className = data.className
            this.materialInfo.classPath = nodePath
            if (!data.children) {
                this.materialInfo.className = data.className
                this.materialInfo.classId = data.classId
            }
            console.log(data, nodePath)
            this.getTableData()
        },
        // 修改
        updateM () {
            this.$refs.formEdit.validate(async valid => {
                if (!valid) return
                this.materialInfo.classPath = ''
                this.clientPop('info', '你确定编辑用户信息吗？', async () => {
                    let res = { code: 200 }
                    if (res.code !== 200) return
                    this.$message({ type: 'success', message: '用户编辑成功' })
                    this.getTableData()
                    this.userModal.title = ''
                    this.userModal.visible = false
                })
            })
        },
        onSave (again = false) {
            this.materialInfo.billId = ''
            let a = this.materialInfo.classPath
            this.materialInfo.classPath = []
            this.$refs.formEdit.validate(async valid => {
                if (!valid) return
                let res = { code: 200 }
                if (res.code !== 200) return
                this.$message({ type: 'success', message: '用户新增成功' })
                this.getTableData()
                if (again) {
                    this.materialInfo.billId = ''
                    this.materialInfo.classPath = a
                    return
                }
                this.userModal.title = ''
                this.userModal.visible = false
            })
        },
        // 取消
        closeViewListM () {
            this.materialInfo.userName = ''
            this.materialInfo.name = ''
            this.materialInfo.password = ''
            this.materialInfo.state = 1
            this.materialInfo.phone = ''
            this.materialInfo.role = undefined
            this.materialInfo.classId = ''
            this.materialInfo.className = ''
            this.materialInfo.classPath = []
            this.userModal.title = ''
            this.userModal.visible = false
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        operate_td (title, row) {
            if(title == '新增') {
                this.materialInfo.userName = ''
                this.materialInfo.name = ''
                this.materialInfo.password = ''
                this.materialInfo.state = 1
                this.materialInfo.phone = ''
                this.materialInfo.role = undefined
                this.materialInfo.classId = ''
                this.materialInfo.className = ''
                this.materialInfo.classPath = []
                this.userModal.title = '新增用户'
                this.userModal.visible = true
                this.$refs.formEdit.clearValidate()
            }else if(title == '编辑') {
                this.materialInfo.userName = row.userName
                this.materialInfo.password = row.password
                this.materialInfo.name = row.name
                this.materialInfo.role = row.role
                this.materialInfo.state = row.state
                this.materialInfo.phone = row.phone
                this.materialInfo.classId = row.classId
                this.materialInfo.className = row.className
                this.materialInfo.classPath = row.classPath.split('/')
                this.userModal.title = '编辑用户'
                this.userModal.visible = true
                this.$refs.formEdit.clearValidate()
            }else if (title == '删除') {
                this.clientPop('info', '你确定删除该用户吗？', async () => {
                    let res = { code: 200 }
                    if (res.code !== 200) return
                    this.$message({ type: 'success', message: '用户删除成功' })
                    this.getTableData()
                })
            }else if (title == '重置密码') {
                this.passwordModal.userId = row.userId
                this.passwordModal.visible = true
            }
        },
        // 用户密码重置-确认
        confirm_password () {
            this.$refs.passwordRef.validate(async valid => {
                if (!valid) return
                let res = { code: 200 }
                if (res.code !== 200) return
                this.$message({ type: 'success', message: '用户密码重置成功' })
            })
        },
        // 单个上架
        updateState (row, state, title) {
            let { materialName, topClassName } = row
            if (topClassName !== '低值易耗品') {
                return this.$message({
                    message: materialName + '不是低值易耗品，不能操作',
                    type: 'error'
                })
            }
            this.clientPop('info', '您确定要对物料【' + materialName + '】进行【' + title + '】操作吗?', async () => {
                let res = await batchUpdateMaterialDtlState({ MaterialDtlDTOList: [row], state })
                this.getTableData()
                this.message(res)
            })
        },
        // 批量修改状态
        updateStateBatch (state) {
            if (this.dataListSelections.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let invalidList = this.dataListSelections.filter(item => !(item.topClassName === '低值易耗品'))
            let validList = this.dataListSelections.filter(item => (item.topClassName === '低值易耗品'))
            let MaterialDtlDTOList = []
            if (validList.length === 0) {
                return this.$message('请选择低值易耗品进行操作！')
            } else {
                for (let i = 0; i < validList.length; i++) {
                    MaterialDtlDTOList.push(validList[i])
                }
            }
            if (invalidList.length > 0) {
                let materialNames = invalidList.map(item => item.materialName).join(',')
                this.$message.error(materialNames + '不是低值易耗品，不能操作')
            }

            this.clientPop('info', '您确定要批量启用/停用这些物料吗！', async () => {
                this.tableLoading = true
                try {
                    let res = await batchUpdateMaterialDtlState({ MaterialDtlDTOList, state })
                    this.tableLoading = false
                    this.dataListSelections = []
                    this.message(res)
                    this.getTableData()
                } catch (e) {
                    this.tableLoading = false
                }
            })
        },
        // 获取表格数据
        getTableData () {
            let params = {
                pageIndex: this.paginationInfo.currentPage,
                pageSize: this.paginationInfo.pageSize,
                keyWord: this.init.keyword.trim()
            }
            if (this.init.classId != null) {
                params.classId = this.init.classId
            }
            this.tableLoading = true
            queryPageMaterialDtl(params).then(res => {
                res = {
                    'totalCount': 2,
                    'pageSize': 100,
                    'totalPage': 1,
                    'currPage': 1,
                    'list': [
                        {
                            'userName': 'zhangsan',
                            'name': '张三', password: '123',
                            'phone': '18815420821',
                            'institution': '四川公路桥梁建设集团有限公司',
                            'role': '管理员',
                            'state': '1',
                            classId: 'a92724915394-8f4a-2247-3575-754cb0ea',
                            className: '其它材料',
                            classPath: 'a92724915394-8f4a-2247-3575-754cb0ea'
                        }, {
                            'userName': 'zhangsan',
                            'name': '张三', password: '123',
                            'phone': '18815420821',
                            'institution': '四川公路桥梁建设集团有限公司',
                            'role': '管理员',
                            'state': '0',
                            classId: 'a92724915394-8f4a-2247-3575-754cb0eb',
                            className: '主要材料',
                            classPath: 'a92724915394-8f4a-2247-3575-754cb0eb'
                        }
                    ]
                }
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => this.tableLoading = false)
        },
        // 消息提示
        message (res) {
            if (res.code !== 200) return
            this.$message({ message: res.message, type: 'success' })
        },
        getScreenInfo () {
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        },
        // 是否显示密码
        toggleShowPass () {
            this.hidePass = this.hidePass === this.ico_hide ? this.ico_show : this.ico_hide
            this.pwType = this.pwType === 'text' ? 'password' : 'text'
        },
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type=‘number’] {
    -moz-appearance: textfield !important;
}

.el-dialog__body {
    margin: 220px;
}

.base-page .left {
    min-width: 200px;
    height: 100%;
    padding: 0;
    overflow: scroll;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .el-dropdown {
    min-width: 75px;
    margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-dialog .el-dialog__body {margin: 0;height: auto;}
/deep/ .e-form .buttons {position: relative;background: transparent;}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}
</style>
