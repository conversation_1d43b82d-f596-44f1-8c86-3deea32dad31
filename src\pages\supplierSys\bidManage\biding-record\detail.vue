<template>
  <div class="e-form">
    <BillTop @cancel="handleClose"></BillTop>
    <div class="tabs warningTabs" v-loading="formLoading">
      <el-tabs
        tab-position="left"
        :style="{ height: tabsContentHeight + 'px' }"
        v-model="tabsName"
        @tab-click="onChangeTab"
      >
        <el-tab-pane label="竞价详情" name="baseInfo" :disabled="clickTabFlag">
        </el-tab-pane>
        <el-tab-pane
          label="报名记录"
          name="biddingSuppliers"
          :disabled="clickTabFlag"
        >
        </el-tab-pane>
        <!--        <el-tab-pane
          label="已邀请供应商"
          v-if="formData.type === 2"
          name="invitedSuppliers"
          :disabled="clickTabFlag"
        >
        </el-tab-pane>-->
        <el-tab-pane
          label="审核历史"
          name="auditRecords"
          :disabled="clickTabFlag"
        >
        </el-tab-pane>
        <el-tab-pane
          label="竞标结果"
          name="bidResults"
          :disabled="clickTabFlag"
          v-if="this.$route.query.state !== 1"
        >
        </el-tab-pane>
        <!-- <el-tab-pane
          label="竞价记录"
          name="biddingBidRecords"
          :disabled="clickTabFlag"
          v-if="formData.biddingState == 3"
        >
        </el-tab-pane>
        <el-tab-pane
          label="报名记录"
          name="biddingSuppliers"
          :disabled="clickTabFlag"
        >
        </el-tab-pane> -->
        <div
          id="tabs-content"
          :style="{ height: tabsContentHeight - 40 + 'px' }"
        >
          <!-- 竞价信息 -->
          <div id="baseInfCon" class="con">
            <div class="tabs-title" id="baseInfo">竞价信息</div>
            <div style="width: 100%" class="form">
              <el-form
                :model="formData"
                :rules="formDataRules"
                label-width="200px"
                ref="rulesBas2323e"
                class="demo-ruleForm"
              >
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="竞价采购编号：">
                      <span>{{ formData.biddingSn }}</span>
                      <el-button
                        size="small"
                        type="primary"
                        style="margin-left: 6px"
                        >查看发布竞价</el-button
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="标题：" prop="title">
                      <el-input
                        v-if="formData.state == 0 || formData.state == 2"
                        placeholder="请输入标题"
                        clearable
                        v-model="formData.title"
                      ></el-input>
                      <span v-else>{{ formData.title }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="发布时间：">
                      <span>{{ formData.startTime }}</span>
                    </el-form-item>
                  </el-col>
                  <!--                                    <el-col :span="12">-->
                  <!--                                        <el-form-item label="公示状态：">-->
                  <!--                                            <el-tag v-if="formData.publicityState == 0" type="info">未发布</el-tag>-->
                  <!--                                            <el-tag type="success" v-if="formData.publicityState == 1">已发布</el-tag>-->
                  <!--                                        </el-form-item>-->
                  <!--                                    </el-col>-->
                  <el-col :span="12">
                    <el-form-item label="截止时间：" prop="endTime">
                      <el-date-picker
                        v-if="formData.state == 0 || formData.state == 2"
                        v-model="formData.endTime"
                        type="datetime"
                        placeholder="选择日期时间"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :picker-options="pickerOptions"
                      >
                      </el-date-picker>
                      <!--                                            <el-date-picker-->
                      <!--                                                v-if="formData.state == 0 || formData.state == 2"-->
                      <!--                                                value-format="yyyy-MM-dd HH:mm:ss"-->
                      <!--                                                v-model="formData.endTime"-->
                      <!--                                                align="right"-->
                      <!--                                                type="date"-->
                      <!--                                                placeholder="选择日期"-->
                      <!--                                                :picker-options="pickerOptions"-->
                      <!--                                            ></el-date-picker>-->
                      <span v-else>{{ formData.endTime }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="联系人名称：">
                      <el-input
                        maxlength="20"
                        v-if="formData.state == 0 || formData.state == 2"
                        placeholder="请输入联系人名称"
                        clearable
                        v-model="formData.linkName"
                      ></el-input>
                      <span v-else>{{ formData.linkName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系电话：" prop="linkPhone">
                      <el-input
                        v-if="formData.state == 0 || formData.state == 2"
                        placeholder="请输入联系电话"
                        clearable
                        v-model="formData.linkPhone"
                      ></el-input>
                      <span v-else>{{ formData.linkPhone }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="状态：">
                      <el-tag type="info" v-if="formData.state == 4"
                        >草稿</el-tag
                      >
                      <el-tag type="danger" v-if="formData.state == 8"
                        >审核失败</el-tag
                      >
                      <el-tag type="" v-if="formData.state == 6">审核中</el-tag>
                      <el-tag type="success" v-if="formData.state == 7"
                        >已中标</el-tag
                      >
                      <el-tag type="danger" v-if="formData.state == 9"
                        >已流标</el-tag
                      >
                      <el-tag type="danger" v-if="formData.state == 10"
                        >已作废</el-tag
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="竞价类型：">
                      <span v-if="formData.type == 1"
                        ><el-tag type="success">公开竞价</el-tag></span
                      >
                      <span v-if="formData.type == 2">
                        <el-tag>邀请竞价</el-tag></span
                      >
                    </el-form-item>
                  </el-col>
                  <!--                  <el-col :span="12">
                    <el-form-item label="时间状态：">
                      <el-tag v-if="formData.biddingState == 1" type="info"
                        >未开始</el-tag
                      >
                      <el-tag type="success" v-if="formData.biddingState == 2"
                        >进行中</el-tag
                      >
                      <el-tag type="danger" v-if="formData.biddingState == 3"
                        >已结束</el-tag
                      >
                    </el-form-item>
                  </el-col>-->
                </el-row>
                <el-row>
                  <!--                                    <el-col :span="12">-->
                  <!--                                        <el-form-item label="是否中标：">-->
                  <!--                                            <el-tag type="info" v-if="formData.isHitBidding == 0">否</el-tag>-->
                  <!--                                            <el-tag type="success" v-if="formData.isHitBidding == 1">是</el-tag>-->
                  <!--                                        </el-form-item>-->
                  <!--                                    </el-col>-->
                  <el-col :span="12">
                    <el-form-item label="创建时间：">
                      <span>{{ formData.gmtCreate }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="经办人：">
                      <el-input
                        placeholder="请选择经办人"
                        disabled
                        v-model="formData.name"
                      />
                      <el-button
                        size="mini"
                        type="primary"
                        @click="importOperatorSelect"
                        >选择</el-button
                      >
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row
                  v-if="formData.productType == 1 || formData.productType == 2"
                >
                  <el-col :span="12">
                    <el-form-item label="价格类型：">
                      <el-tag type="" v-if="formData.billType == 1"
                        >浮动价格</el-tag
                      >
                      <el-tag type="" v-if="formData.billType == 2"
                        >固定价格</el-tag
                      >
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="竞价说明：">
                      <editor
                        v-model="formData.biddingExplain"
                        :disabled="!(formData.state == 0 || formData == 2)"
                        style="width: 100%"
                      ></editor>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="formData.cancelResultType != null && formData.cancelResultType != ''">
                  <el-col :span="12" >
                    <el-form-item label="作废原因：" >
                      <el-select
                        v-model="formData.cancelResultType"
                        disabled
                        style="width: 100%; margin-right: 20px"
                      >
                        <el-option
                          v-for="item in cancelResultOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item >
                      <el-input disabled v-model="formData.cancelRemark"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <div id="biddingSuppliersCon" class="con">
            <div class="tabs-title" id="biddingSuppliers">报名记录</div>
            <div class="e-table" style="background-color: #fff">
              <el-button
                class="btn-blue"
                style="margin-bottom: 1%"
                @click="failureOfBid"
                >流标</el-button
              >
              <el-table
                border
                style="width: 100%"
                :span-method="objectSpanMethod"
                :data="formData.biddingBidRecordsMap.materialsData"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
              >
                <el-table-column
                  label="序号"
                  prop="xh"
                  width="60"
                ></el-table-column>
                <el-table-column label="操作" width="230">
                  <template v-slot="scope">
                    <el-button
                      type="text"
                      v-if="
                        $route.query.state !== 1 && scope.row.bidState === 0
                      "
                      @click="biddingOrder(scope.row)"
                      >拟中标</el-button
                    >
                    <el-button
                      type="text"
                      v-if="
                        $route.query.state !== 1 && scope.row.bidState !== 0
                      "
                      @click="biddingOrderCancel(scope.row)"
                      >取消中标</el-button
                    >
                    <el-button type="text" @click="openShopDow(scope.row)"
                      >下载附件</el-button
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  prop="supplierName"
                  label="报价单位"
                  width="200"
                />
                <el-table-column
                  prop="productName"
                  label="物资名称"
                  width="200"
                />
                <el-table-column prop="spec" label="规格型号" width="200" />
                <el-table-column
                  prop="productTexture"
                  label="材质"
                  width="200"
                />
                <el-table-column prop="unit" label="计量单位" width="200" />
                <el-table-column
                  prop="deliveryAddress"
                  label="供货地点"
                  width="200"
                />
                <el-table-column
                  prop="deliveryDate"
                  label="供货时间"
                  width="200"
                />
                <el-table-column label="供应商报价情况">
                  <el-table-column prop="num" label="预估数量" width="200" />
                  <el-table-column
                    prop="bidRatePrice"
                    label="含税到场单价"
                    width="200"
                  />
                  <el-table-column
                    prop="bidRateAmount"
                    label="含税金额"
                    width="200"
                  />
                </el-table-column>
                <el-table-column label="专区销售定价">
                  <el-table-column
                    prop="monthlyDifference"
                    label="上下游账期月数差"
                    width="200"
                  >
                    <template v-slot="scope">
                      <el-input
                        @input="
                          accountPeriodGapChange(scope.row.id)
                        "
                        v-model="scope.row.monthlyDifference"
                        v-if="
                          $route.query.state !== 1 &&
                          scope.row.productName !== '小计'
                        "
                      ></el-input>
                      <span v-else>{{ scope.row.monthlyDifference }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="unitPriceIncludingTax"
                    label="含税拟销售单价（元）"
                    width="200"
                  />
                  <el-table-column
                    prop="taxInclusiveAmount"
                    label="含税拟销售金额（元）"
                    width="200"
                  />
                </el-table-column>
              </el-table>
            </div>
          </div>
          <!-- 成交结果 -->
          <div id="dealResultCon" class="con">
            <div class="tabs-title" id="dealResult">成交结果</div>
            <div v-html="formData.dealResult" class="formatted-text"></div>
          </div>

          <div id="auditRecordsCon" class="con">
            <div class="tabs-title" id="auditRecords">审核历史</div>
            <div class="e-table" style="background-color: #fff">
              <el-table
                border
                style="width: 100%"
                :data="formData.auditRecords"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  width="60"
                ></el-table-column>
                <!--                <el-table-column prop="auditType" label="审核类型" width="160">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                    <el-tag v-if="scope.row.auditType == 2">变更审核</el-tag>
                  </template>
                </el-table-column>-->
                <el-table-column
                  prop="reviewSecondary"
                  label="审核级次"
                  width="200"
                >
                </el-table-column>
                <el-table-column prop="auditor" label="审核人" width="200">
                </el-table-column>
                <el-table-column prop="noticeTime" label="通知时间" width="160">
                </el-table-column>
                <el-table-column prop="auditTime" label="审核时间" width="160">
                </el-table-column>
                <el-table-column prop="auditRemark" label="审核意见" width="">
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div
            id="bidResultsCon"
            class="con"
            v-if="this.$route.query.state !== 1"
          >
            <div class="tabs-title" id="bidResults">竞价结果</div>
            <div class="e-table" style="background-color: #fff">
              <el-table
                border
                style="width: 100%"
                :span-method="objectSpanMethod"
                :data="formData.winningBidResult"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
              >
                <el-table-column
                  label="序号"
                  prop="xh"
                  width="60"
                ></el-table-column>
                <el-table-column
                  prop="supplierName"
                  label="中标单位"
                  width="200"
                />
                <el-table-column
                  prop="productName"
                  label="物资名称"
                  width="200"
                />
                <el-table-column prop="spec" label="规格型号" width="200" />
                <el-table-column
                  prop="productTexture"
                  label="材质"
                  width="200"
                />
                <el-table-column prop="unit" label="计量单位" width="200" />
                <el-table-column
                  prop="deliveryAddress"
                  label="供货地点"
                  width="200"
                />
                <el-table-column
                  prop="deliveryDate"
                  label="供货时间"
                  width="200"
                />
                <el-table-column label="供应商报价情况">
                  <el-table-column prop="num" label="预估数量" width="200" />
                  <el-table-column
                    prop="bidRatePrice"
                    label="含税到场单价"
                    width="200"
                  />
                  <el-table-column
                    prop="bidRateAmount"
                    label="含税金额"
                    width="200"
                  />
                </el-table-column>
                <el-table-column label="专区销售定价">
                  <el-table-column
                    prop="monthlyDifference"
                    label="上下游账期月数差"
                    width="200"
                  />
                  <el-table-column
                    prop="unitPriceIncludingTax"
                    label="含税拟销售单价（元）"
                    width="200"
                  />
                  <el-table-column
                    prop="taxInclusiveAmount"
                    label="含税拟销售金额（元）"
                    width="200"
                  />
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tabs>
      <!--            竞价记录详情-->
      <el-dialog
        v-dialogDrag
        id="supplierDialog"
        title="竞价记录详情"
        :visible.sync="showBidingRecord"
        width="80%"
        style="margin-left: 10%"
        :close-on-click-modal="false"
      >
        <div
          class="e-table"
          style="background-color: #fff"
          v-loading="showBidingRecordLoading"
        >
          <div class="tabs-title" id="contractList">竞价记录信息</div>
          <el-form
            :model="biddingRecordForm"
            label-width="200px"
            ref="rulesBase"
            class="demo-ruleForm"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="竞价记录编号：">
                  <span>{{ biddingRecordForm.bidRecordSn }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="机构名称：">
                  <span>{{ biddingRecordForm.supplierName }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row> </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="不含税总金额：">
                  <span>{{ biddingRecordForm.bidAmount }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="含税总金额：">
                  <span>{{ biddingRecordForm.bidRateAmount }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="状态：">
                  <el-tag type="info" v-if="biddingRecordForm.state == 1"
                    >初始</el-tag
                  >
                  <el-tag v-if="biddingRecordForm.state == 5"
                    >中标待审核</el-tag
                  >
                  <el-tag type="success" v-if="biddingRecordForm.state == 6"
                    >已中标</el-tag
                  >
                  <el-tag type="danger" v-if="biddingRecordForm.state == 7"
                    >中标审核失败</el-tag
                  >
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报价时间：">
                  <span>{{ biddingRecordForm.bidTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="附件名称：">
                  <span>{{ biddingRecordForm.fileName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注：">
                  <span>{{ biddingRecordForm.remarks }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="tabs-title" id="contractList">竞价记录明细</div>
          <el-table
            border
            style="width: 100%"
            :data="biddingRecordForm.pageList.list"
            class="table"
            :max-height="$store.state.tableHeight"
          >
            <el-table-column
              label="序号"
              type="index"
              width="60"
            ></el-table-column>
            <el-table-column
              prop="productName"
              label="商品名称"
            ></el-table-column>
            <el-table-column prop="spec" label="规格型号"></el-table-column>
            <el-table-column
              prop="productTexture"
              label="商品材质"
            ></el-table-column>
            <el-table-column prop="unit" label="计量单位"></el-table-column>
            <el-table-column prop="num" label="数量"></el-table-column>
            <el-table-column
              prop="bidPrice"
              label="不含税到场单价"
            ></el-table-column>
            <el-table-column prop="taxRate" label="税率"></el-table-column>
            <el-table-column
              prop="bidRatePrice"
              label="含税到场单价"
            ></el-table-column>
            <el-table-column
              prop="bidRateAmount"
              label="含税总金额"
            ></el-table-column>
            <el-table-column
              prop="bidAmount"
              label="不含税总金额"
            ></el-table-column>
            <el-table-column
              prop="remarks"
              label="报价方备注"
              width="160"
            ></el-table-column>
          </el-table>
          <div class="tabs-title" id="auditRecords">审核历史</div>
          <div class="e-table" style="background-color: #fff">
            <el-table
              border
              style="width: 100%"
              :data="biddingRecordForm.auditRecordList"
              :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
              :row-style="{ fontSize: '14px', height: '48px' }"
            >
              <el-table-column
                label="序号"
                type="index"
                width="60"
              ></el-table-column>
              <el-table-column prop="auditType" label="审核类型" width="160">
                <template v-slot="scope">
                  <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                  <el-tag v-if="scope.row.auditType == 2">变更审核</el-tag>
                  <el-tag v-if="scope.row.auditType == 6">竞价中标审核</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="founderName" label="审核人" width="200">
              </el-table-column>
              <el-table-column prop="gmtCreate" label="审核时间" width="160">
              </el-table-column>
              <el-table-column prop="auditResult" label="审核意见">
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="buttons">
          <el-button
            v-if="biddingRecordForm.state == 5"
            v-has-permi="{
              platform: 'oneselfShopManage',
              auth: 'hitTarget-bid',
            }"
            class="btn-greenYellow"
            @click="hitBiddingClickM(1, '通过')"
            >通过
          </el-button>
          <el-button
            v-if="biddingRecordForm.state == 5"
            v-has-permi="{
              platform: 'oneselfShopManage',
              auth: 'hitTarget-bid',
            }"
            class="btn-delete"
            @click="hitBiddingClickM(0, '不通过')"
            >不通过
          </el-button>
          <el-button
            v-if="biddingRecordForm.fileFarId != null"
            class="btn-blue"
            v-has-permi="{
              platform: 'oneselfShopManage',
              auth: 'hitTarget-bid',
            }"
            @click="openShopDow(biddingRecordForm)"
            >下载附件
          </el-button>
          <el-button @click="showBidingRecord = false">关闭</el-button>
        </div>
      </el-dialog>
      <div class="buttons">
        <el-button
          v-if="formData.state == 4"
          class="btn-greenYellow"
          @click="save(0)"
          >保存
        </el-button>
        <el-button
          v-if="
            formData.state == 4 && userInfo.roles.includes('物资竞价提交权限')
          "
          v-has-permi="{ platform: 'oneselfShopManage', auth: 'submit-bid' }"
          class="btn-blue"
          @click="save(1)"
          >保存并提交至审核
        </el-button>
        <!-- <el-button
          v-if="biddingRecordForm.state == 5"
          class="btn-greenYellow"
          v-has-permi="{
            platform: 'oneselfShopManage',
            auth: 'submitAudit-bid',
          }"
          @click="checkBidingClick(1, '通过')"
          >通过
        </el-button>
        <el-button
          v-if="biddingRecordForm.state == 5"
          class="btn-delete"
          v-has-permi="{
            platform: 'oneselfShopManage',
            auth: 'submitAudit-bid',
          }"
          @click="checkBidingClick(0, '不通过')"
          >不通过
        </el-button> -->
        <el-button
          v-if="formData.state == 6"
          v-has-permi="{ platform: 'oneselfShopManage', auth: 'submit-bid' }"
          class="btn-blue"
          @click="checkBidingClick('1')"
          >审核
        </el-button>
        <el-button
          v-has-permi="{ platform: 'oneselfShopManage', auth: 'submit-bid' }"
          class="btn-blue"
          @click="revoke()"
          >撤回
        </el-button>
        <el-button
          class="btn-delete"
          v-has-permi="{
            platform: 'oneselfShopManage',
            auth: 'submitAudit-bid',
          }"
          @click="checkBidingClick('2')"
          >作废
        </el-button>
        <el-button @click="handleClose">返回</el-button>
      </div>
    </div>
    <dialog-model ref="dialogModelRef" @submitForm="submitForm"></dialog-model>
    <el-dialog
      v-dialogDrag
      v-loading="operatorTableLoading"
      :close-on-click-modal="false"
      :visible.sync="showOperatorDialog"
      style="margin-left: 20%"
      title="选择经办人"
      width="60%"
    >
      <div class="e-table" style="background-color: #fff">
        <div class="top" style="height: 50px; padding-left: 10px">
          <div class="left">
            <el-input
              v-model="operator.keyWord"
              clearable
              placeholder="输入搜索关键字"
              type="text"
              @blur="getOperatorList"
            >
              <img
                slot="suffix"
                :src="require('@/assets/search.png')"
                @click="getOperatorList"
              />
            </el-input>
          </div>
        </div>
        <el-table
          ref="tableRef"
          :data="operator.tableData"
          :max-height="$store.state.tableHeight"
          border
          class="table"
          highlight-current-row
          style="width: 100%"
          @row-click="handleCurrentOperatorClick"
        >
          <el-table-column
            label="序号"
            type="index"
            width="60"
          ></el-table-column>
          <el-table-column
            label="人员名称"
            prop="name"
            width="200"
          ></el-table-column>
          <el-table-column
            label="所属单位"
            prop="supplierName"
          ></el-table-column>
        </el-table>
      </div>
      <span slot="footer">
        <Pagination
          v-show="operator.tableData && operator.tableData.length > 0"
          :currentPage.sync="operator.paginationInfo.currentPage"
          :pageSize.sync="operator.paginationInfo.pageSize"
          :total="operator.paginationInfo.total"
          @currentChange="getOperatorList"
          @sizeChange="getOperatorList"
        />
        <el-button style="margin-top: 20px" @click="showOperatorDialog = false"
          >取消</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import editor from '@/components/quillEditor'
import '@/utils/jquery.scrollTo.min'
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import Pagination from '@/components/pagination/pagination'
import DialogModel from '@/components/dialog-model.vue'
import {
    /*auditBidingInfo,*/
    auditHitBidding,
    bidingRecordListByEntity,
    updateBidingByOrder,
    getRecordBiddingInfo,
    loseEfficacyBidding,
    listBidingOrderListIds,
    updateAuditStatus,
} from '@/api/shopManage/biding/biding'
import {
    previewFile,
    // uploadFile,
    // thumbnailImage,
} from '@/api/platform/common/file'
// import moment from 'moment'
// import { exportBiddingBidRecords } from '@/api/supplierSys/bidManage/myBidding/myBidManage'
// import myPagination from '@/components/pagination/myPagination'
// import { suppliersSynthesizeTemporaryGetBySn } from '@/api/frontStage/userCenter'
// import JSZip from 'jszip'
// import FileSaver from 'file-saver'
//import AuditFrom from '@/components/reconciliation/auditFrom.vue'

export default {
    data () {
        return {
            cancelResultOptions: [
                {
                    value: 1,
                    label: '数据错误',
                },
                {
                    value: 2,
                    label: '不符合业务要求',
                },
                {
                    value: 3,
                    label: '不在此功能录入',
                },
                {
                    value: 4,
                    label: '其他',
                },
            ],
            bidStateValue: 0,
            xhValue: 0,
            biddingRecordsloading: false,
            currentBidRecordId: null,
            biddingRecordForm: {
                pageList: {
                    list: [],
                },
            },
            showBidingRecord: false,
            showBidingRecordLoading: false,
            pickerOptions: {
                disabledDate (time) {
                    // 获取今天的日期（不包括时间）
                    const today = new Date()
                    // 将传入的时间转换为日期对象（不包括时间）
                    const selectedDate = new Date(time)
                    selectedDate.setHours(0, 0, 0, 0)

                    // 如果选择的日期在今天之后或者是今天，则禁用
                    return selectedDate.getTime() < today.getTime()
                },
            },
            bidingOrderList: [],
            formLoading: false,
            orderSn: null,
            biddingOrderListLoading: false,
            biddingOrderCompany: '',
            biddingRow: {},
            //基本信息表单数据
            formData: {},
            files: [],
            formDataRules: {
                title: [
                    { required: true, message: '请输入标题', trigger: 'blur' },
                    { min: 1, max: 200, message: '超过限制', trigger: 'blur' },
                ],
                linkPhone: [
                    { required: false, message: '请输入11位手机号', trigger: 'blur' },
                    {
                        pattern: /^1[3456789]\d{9}$/,
                        message: '请输入正确的手机号',
                        trigger: 'blur',
                    },
                ],
            },
            // 表格数据
            tableData: [],
            paginationInfo: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationInfo4: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            paginationOrderList: {
                // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            showOperatorDialog: false,
            operatorTableLoading: false,
            operator: {
                tableData: [],
                keyWord: null,
                paginationInfo: {
                    // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 0,
                },
            },
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: [],
            inventoryList: [],
            productNum: 0,
        }
    },
    components: {
    /*AuditFrom,*/
        Pagination,
        editor,
        DialogModel,
    },
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) return
            let newDateSr = dateStr.split(' ')
            return newDateSr[0]
        },
    },
    created () {
        this.getFormData()
    },
    mounted () {
    // 获取数据
    // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'biddingSuppliers', 'auditRecords']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        synthesizeTemporarySn () {
            if (this.formData.biddingSourceType === 3) {
                const sn = this.formData.biddingProducts.map(
                    t => t.synthesizeTemporarySn
                )[0]
                return sn
            } else return ''
        },
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight
        },
        // 使用计算属性处理文本，替换换行符为 <br> 标签
        formattedText () {
            return this.formData.biddingNotice.replace(/\n/g, '<br>')
        },
        // 左侧树高度
        leftTreeHeight () {
            return this.screenHeight - 21 + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return this.screenWidth - 302 + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return this.screenWidth - 300 + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            },
        },
    },
    methods: {
        listBidingOrderListIdsM () {
            let params = {
                page: this.paginationOrderList.currentPage,
                limit: this.paginationOrderList.pageSize,
            }
            if (this.orderSn != null) {
                params.orderSn = this.orderSn
            }
            if (this.formData.productType != null) {
                if (this.formData.productType == 0) {
                    params.productType = 10
                }
                if (this.formData.productType == 1) {
                    params.productType = 13
                    params.billType = this.formData.billType
                }
            }
            this.biddingOrderListLoading = true
            listBidingOrderListIds(params)
                .then(res => {
                    this.paginationOrderList.total = res.totalCount
                    this.paginationOrderList.pageSize = res.pageSize
                    this.paginationOrderList.currentPage = res.currPage
                    this.bidingOrderList = res.list
                })
                .finally(() => {
                    this.biddingOrderListLoading = false
                })
        },
        failureOfBid () {
            this.clientPop(
                'info',
                '您确定要进行流标操作吗？对应订单商品会变成待竞价状态！',
                async () => {
                    this.formLoading = true
                    loseEfficacyBidding({ biddingId: this.formData.biddingId })
                        .then(res => {
                            if (res.code != null && res.code === 200) {
                                this.$message.success('操作成功')
                                this.getFormData()
                            }
                        })
                        .finally(() => {
                            this.formLoading = false
                        })
                }
            )
        },
        openShopDow (fileRow) {
            this.biddingRecordsloading = true
            previewFile({ recordId: fileRow.bidRecordId })
                .then(res => {
                    const blob = new Blob([res])
                    const url = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = fileRow.fileName
                    a.click()
                    window.URL.revokeObjectURL(url)
                })
                .finally(() => {
                    this.biddingRecordsloading = false
                })
        },
        objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
            console.log('row', row, column, rowIndex, columnIndex)
            if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
                if (rowIndex % (this.productNum + 1) === 0) {
                    return {
                        rowspan: this.productNum + 1,
                        colspan: 1,
                    }
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0,
                    }
                }
            }
            if ((rowIndex + 1) % (this.productNum + 1) === 0) {
                if (columnIndex === 3) {
                    return [1, 6]
                }
            }
        },
        hitBiddingClickM (state, title) {
            this.clientPop(
                'info',
                '您确定进行【' + title + '】操作吗？',
                async () => {
                    if (state == 0) {
                        this.$prompt('未通过原因', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'error',
                            inputType: 'textarea',
                            inputPlaceholder: '请输入不通过原因',
                            inputPattern: /^.+$/,
                            inputErrorMessage: '请输入不通过原因',
                        })
                            .then(({ value }) => {
                                let params = {
                                    biddingId: this.formData.biddingId,
                                    isOpen: 0,
                                    auditResult: value,
                                    bidRecordId: this.biddingRecordForm.bidRecordId,
                                }
                                this.showBidingRecordLoading = true
                                auditHitBidding(params).then(res => {
                                    if (res.code != null && res.code == 200) {
                                        this.$message.success('操作成功')
                                        this.getFormData()
                                        this.bidingRecordListByEntityM()
                                    }
                                })
                            })
                            .finally(() => {
                                this.showBidingRecordLoading = false
                            })
                    } else {
                        let params = {
                            biddingId: this.formData.biddingId,
                            isOpen: 1,
                            bidRecordId: this.biddingRecordForm.bidRecordId,
                        }
                        this.showBidingRecordLoading = true
                        auditHitBidding(params)
                            .then(res => {
                                if (res.code != null && res.code == 200) {
                                    this.$message.success('操作成功')
                                    this.getFormData()
                                    this.bidingRecordListByEntityM()
                                }
                            })
                            .finally(() => {
                                this.showBidingRecordLoading = false
                            })
                    }
                }
            )
        },
        submitForm (data, type) {
            if (type == '1') {
                this.formData.pageType = 1
                this.formData.auditResultType = data.auditResultType
                if (this.xhValue == 1 && this.bidStateValue == 1) {
                    this.formData.auditProcessType = 1
                } else {
                    this.formData.auditProcessType = 2
                }
            }

            if (type == '2') {
                this.formData.pageType = 2
                this.formData.cancelResultType = data.cancelResultType
            }
            this.formData.auditResult = data.remark
            this.formData.bidRecordId = this.$route.query.bidRecordId
            this.formData.biddingId = this.$route.query.biddingId
            updateAuditStatus(this.formData)
                .then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.changedRow = []
                        this.getFormData()
                    }
                })
                .finally(() => {
                    this.formLoading = false
                })
        },
        save (num) {
            this.formData.pageType = 4
            this.formData.biddingId = this.$route.query.biddingId
            // this.formData.dtos = this.changedRow
            this.formData.operatorId = this.formData.name
            this.formData.biddingRemark = this.formData.biddingExplain
            this.formData.bidOfferInfoVos =
        this.formData.biddingBidRecordsMap.materialsData
            if (num != null && num == 1) {
                if (
                    this.biddingOrderCompany !== null &&
          this.biddingOrderCompany !== ''
                ) {
                    this.formData.isSubmit = 1
                    this.clientPop('info', '您确定要保存并提交至审核吗？', async () => {
                        this.formLoading = true
                        if (this.xhValue == 1 && this.bidStateValue == 1) {
                            this.formData.auditProcessType = 1
                        } else {
                            this.formData.auditProcessType = 2
                        }
                        updateAuditStatus(this.formData)
                            .then(res => {
                                if (res.code != null && res.code == 200) {
                                    this.$message.success('操作成功')
                                    this.changedRow = []
                                    this.getFormData()
                                }
                            })
                            .finally(() => {
                                this.formLoading = false
                            })
                    })
                } else {
                    this.$message.warning('请确定一个拟中标单位，再提交审核')
                }
            } else {
                this.formLoading = true
                this.formData.isSubmit = 0
                updateAuditStatus(this.formData)
                    .then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('保存成功')
                            this.changedRow = []
                            this.getFormData()
                        }
                    })
                    .finally(() => {
                        this.formLoading = false
                    })
            }
        },
        revoke () {
            this.$confirm('确定要撤回吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                this.formLoading = true
                updateBidingByOrder(this.formData)
                    .then(res => {
                        if (res.code != null && res.code === 200) {
                            this.$message.success('操作成功')
                            this.changedRow = []
                            this.getFormData()
                        }
                    })
                    .finally(() => {
                        this.formLoading = false
                    })
            })
        },
        checkBidingClick (dialogType) {
            this.$refs.dialogModelRef.init(dialogType)
            /*this.clientPop(
                'info',
                '您确定进行【' + title + '】操作吗？',
                async () => {
                    if (state == 0) {
                        this.$prompt('未通过原因', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'error',
                            inputType: 'textarea',
                            inputPlaceholder: '请输入不通过原因',
                            inputPattern: /^.+$/,
                            inputErrorMessage: '请输入不通过原因',
                        })
                            .then(({ value }) => {
                                let params = {
                                    biddingId: this.formData.biddingId,
                                    isOpen: 0,
                                    auditResult: value,
                                }
                                this.formLoading = true
                                auditBidingInfo(params).then(res => {
                                    if (res.code != null && res.code == 200) {
                                        this.$message.success('操作成功')
                                        this.getFormData()
                                    }
                                })
                            })
                            .finally(() => {
                                this.formLoading = false
                            })
                    } else {
                        let params = {
                            biddingId: this.formData.biddingId,
                            isOpen: 1,
                        }
                        this.formLoading = true
                        auditBidingInfo(params)
                            .then(res => {
                                if (res.code != null && res.code == 200) {
                                    this.$message.success('操作成功')
                                    this.getFormData()
                                }
                            })
                            .finally(() => {
                                this.formLoading = false
                            })
                    }
                }
            )*/
        },
        bidingRecordListByEntityM () {
            let params = {
                page: this.paginationInfo4.currentPage,
                limit: this.paginationInfo4.pageSize,
            }
            if (this.currentBidRecordId != null) {
                params.bidRecordId = this.currentBidRecordId
            }
            this.showBidingRecordLoading = true
            bidingRecordListByEntity(params)
                .then(res => {
                    this.paginationInfo4.total = res.pageList.totalCount
                    this.paginationInfo4.pageSize = res.pageList.pageSize
                    this.paginationInfo4.currentPage = res.pageList.currPage
                    this.biddingRecordForm = res
                })
                .finally(() => {
                    this.showBidingRecordLoading = false
                })
        },
        getFormData () {
            this.formLoading = true
            getRecordBiddingInfo({ biddingId: this.$route.query.biddingId })
                .then(res => {
                    this.biddingOrderCompany = res.biddingOrderCompany
                    if (
                        this.biddingOrderCompany !== null &&
            this.biddingOrderCompany !== ''
                    ) {
                        res.dealResult =
              '建议：根据本次竞价情况，拟选择综合报价最低的<span style="color:red;">' +
              this.biddingOrderCompany +
              '</span>作为本次竟价成交单位，其报价拟确定为上游采购成交价。同时，以表中拟销售价格向项目部招价、销售价格=上游采购成交价(1+3%)*(1+45%/12=账期月数差)。妥否，请审批!(参与竞价供应商报价详情见附件。)'
                        this.biddingRow = res.biddingBidRecordsMap.materialsData.filter(item => item.bidState === 1)[0]
                    } else {
                        res.dealResult = ''
                        this.biddingRow = {}
                    }
                    this.formData = res
                    this.productNum = this.formData.biddingBidRecordsMap.materialsNum
                    this.$nextTick(() => {
                        if (this.$refs.masterOrderItemRef) {
                            // 假设表格的ref属性设置为myTable
                            this.$refs.masterOrderItemRef.doLayout()
                        }
                    })
                })
                .finally(() => {
                    this.formLoading = false
                })
        },
        biddingOrder (row) {
            this.bidStateValue = 1
            this.xhValue = row.xh
            if (
                this.biddingOrderCompany === '' ||
        this.biddingOrderCompany === null
            ) {
                this.biddingRow = row
                this.biddingOrderCompany = row.supplierName
                this.formData.dealResult =
          '建议：根据本次竞价情况，拟选择综合报价最低的<span style="color:red;">' +
          this.biddingOrderCompany +
          '</span>作为本次竟价成交单位，其报价拟确定为上游采购成交价。同时，以表中拟销售价格向项目部招价、销售价格=上游采购成交价(1+3%)*(1+45%/12=账期月数差)。妥否，请审批!(参与竞价供应商报价详情见附件。)'
                this.formData.biddingBidRecordsMap.materialsData.filter(
                    item => item.id === row.id
                )[0].bidState = 1
            } else {
                this.$confirm('你已选择拟中标公司，请问确认要更换该公司吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(() => {
                    this.formData.biddingBidRecordsMap.materialsData.filter(
                        item => item.id === this.biddingRow.id
                    )[0].bidState = 0
                    this.biddingRow = row
                    this.biddingOrderCompany = row.supplierName
                    this.formData.dealResult =
            '建议：根据本次竞价情况，拟选择综合报价最低的<span style="color:red;">' +
            this.biddingOrderCompany +
            '</span>作为本次竟价成交单位，其报价拟确定为上游采购成交价。同时，以表中拟销售价格向项目部招价、销售价格=上游采购成交价(1+3%)*(1+45%/12=账期月数差)。妥否，请审批!(参与竞价供应商报价详情见附件。)'
                    this.formData.biddingBidRecordsMap.materialsData.filter(
                        item => item.id === row.id
                    )[0].bidState = 1
                })
            }
        },
        biddingOrderCancel (row) {
            this.biddingRow = {}
            this.bidStateValue = 0
            this.xhValue = row.xh
            this.biddingOrderCompany = ''
            this.formData.dealResult = ''
            this.formData.biddingBidRecordsMap.materialsData.filter(
                item => item.id === row.id
            )[0].bidState = 0
        },
        //取消
        handleClose () {
            this.$router.go(-1)
            // this.$router.replace('/supplierSys/order/searchOrder')
        },
        indexMethod (index) {
            return Math.floor(index / (this.productNum + 1)) + 1
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        accountPeriodGapChange (id) {
            let option = this.formData.biddingBidRecordsMap.materialsData.filter(
                item => item.id === id
            )[0]
            option.unitPriceIncludingTax =
        option.bidRatePrice *
        1.03 *
        (1 + (0.045 / 12) * option.monthlyDifference)
            option.taxInclusiveAmount = option.num * option.unitPriceIncludingTax
        },
        importOperatorSelect () {
            this.showOperatorDialog = true
            this.getOperatorList()
        },
        getOperatorList () {
            /* let params = {
                pageIndex: this.operator.paginationInfo.currentPage,
                pageSize: this.operator.paginationInfo.pageSize,
            }
            if (this.operator.keyWord != null) {
                params.keyWord = this.operator.keyWord
            }
            this.operatorTableLoading = true
            queryPageOperator(params).then(res => {
                this.operator.tableData = res.list
                this.operator.paginationInfo.total = res.totalCount
                this.operator.paginationInfo.pageSize = res.pageSize
                this.operator.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.operatorTableLoading = false
            })*/
            this.operator.tableData = [{ name: '123' }]
            this.operator.paginationInfo.total = 1
        },
        handleCurrentOperatorClick (row) {
            this.formData.name = row.name
            this.showOperatorDialog = false
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight =
            document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
    },
}
</script>

<style lang='scss' scoped>
.warningTabs {
  padding-top: 70px;
}

#tabs-content {
  padding-bottom: 50px;
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-tabs__content {
  &::-webkit-scrollbar {
    width: 0;
  }
}

/deep/ .el-dialog {
  .el-dialog__body {
    height: 780px;
    margin-top: 0;
  }
}

/deep/ #supplierDialog {
  .el-dialog__body {
    height: 580px;
    margin-top: 0;
  }
}
/deep/ .el-dialog.dlg {
  height: 600px;

  .el-dialog__header {
    margin-bottom: 0;
  }

  .el-dialog__body {
    height: 474px;
    margin: 10px;
    display: flex;

    & > div {
      .e-pagination {
        background-color: unset;
      }

      //height: 670px;
      .title {
        height: 22px;
        margin-bottom: 10px;
        padding-left: 26px;
        text-align: left;
        line-height: 22px;
        color: #2e61d7;
        font-weight: bold;
        position: relative;
        display: flex;

        &::before {
          content: '';
          display: block;
          width: 10px;
          height: inherit;
          border-radius: 5px;
          background-color: blue;
          position: absolute;
          left: 10px;
          top: 0;
        }
      }
    }

    .el-input__inner {
      border: 1px solid blue;
      border-radius: 6px;
    }

    .el-input__suffix {
      width: 20px;
    }

    .e-table {
      flex-grow: 1;

      .table {
        height: 100%;
      }
    }

    .box-left {
      width: 260px;
      display: flex;
      flex-direction: column;

      .search {
        padding: 0 10px;
      }
    }

    .box-right {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      & > div {
        display: flex;
        flex-direction: column;
      }

      .top {
        justify-content: left;
        height: 374px;
        margin: 0;
        border-radius: 0;
        box-shadow: unset;
      }

      .bottom {
        flex-grow: 1;
      }
    }
  }

  .el-dialog__footer {
    background-color: #eff2f6;
  }
}
.formatted-text {
  white-space: pre-line; /* 处理换行符显示 */
  border: 1px solid black;
  margin: 0 1%;
  padding: 6px;
  height: 100px;
}
.upload-table {
  text-align: center;
}
</style>
