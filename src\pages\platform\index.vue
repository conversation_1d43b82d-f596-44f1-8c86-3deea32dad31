<template>
    <div class="page">
        <div class="full" style="padding-right: 11px;">
            <div class="menu full" v-show="showMenu">
                <div class="title">
                    <img class="logo" src="../../assets/images/wuzi_backend.png" alt="">
                </div>
                <el-menu
                    :default-active="defaultActive"
                    class="el-menu-vertical-demo"
                    mode="vertical"
                    background-color="#ffffff00"
                    text-color="#fff"
                    active-text-color="#FFD41C"
                    :unique-opened="true"
                    menu-trigger="click"
                    :router="true"
                    @open="handleSubOpen"
                >
                    <template v-for="item in visibleMenu">
                        <!-- 包含1子菜单 -->
                        <template v-if="item.children">
                            <el-submenu
                                :key="item.menuId"
                                :index="item.menuId.toString()"
                                @click="changePath(1, item.menuName)"
                            >
                                <template slot="title">
                                    <div class="bar"></div>
                                    <img :src="images.gear2" style="margin-right: 10px"  alt=""/>
                                    <span>{{ item.menuName }}</span>
                                </template>
                                <div class="menu-item-box">
                                    <template>
                                        <el-menu-item
                                            v-for="subItem in item.children"
                                            @click="changePath(2, subItem.menuName)"
                                            :index="subItem.menuId"
                                            :route="subItem.route"
                                            :key="subItem.menuId"
                                        >
                                            <template slot="title">
                                                <img :src="images.dot" alt="" />
                                                <span>{{ subItem.menuName }}</span>
                                            </template>
                                        </el-menu-item>
                                    </template>
                                </div>
                            </el-submenu>
                        </template>
                        <!-- 不包含子菜单 -->
                        <template v-else>
                            <el-menu-item
                                :index="item.menuId"
                                :route="item.route"
                                @click="changePath(0, item.menuName)"
                                :key="item.menuId"
                            >
                                <div class="bar"></div>
                                <img :src="images.gear2" style="margin-right: 10px" alt="" />
                                <span slot="title">{{ item.menuName }}</span>
                            </el-menu-item>
                        </template>
                    </template>
                </el-menu>
            </div>
            <div id="fold-btn" @click="showMenu = !showMenu"></div>
        </div>
        <div class="table-box">
            <div class="history">
                <top-btn-bar></top-btn-bar>
            </div>
            <div style="height: 100%; background-color: #fff;">
                <div class="router-box">
                    <top-step :stepInfo="currentSteps" v-show="showSteps" />
                    <keep-alive>
                        <router-view v-if="$route.meta.keepAlive"></router-view>
                    </keep-alive>
                    <router-view v-if="!$route.meta.keepAlive"></router-view>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery'
import topStep from '../../components/topstep/topstep'
import topBtnBar from '../../components/topButtonBar'
import foldBtn from '../../assets/menu_close.png'
import openBtn from '../../assets/menu_open.png'
import { mapState, mapActions } from 'vuex'

export default {
    components: { topStep, topBtnBar },
    watch: {
        showMenu: {
            handler (newVal) {
                let btnImg = newVal ? `url(${foldBtn})` : `url(${openBtn})`
                $('#fold-btn').css('background-image', btnImg)
            }
        },
        $route: {
            handler () {
                this.setDefaultActive()
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        showSteps () {
            let whiteList = ['/platform/fee/yearPayRecordDtl', '/platform/fee/dealPayRecordDetail', '/platform/fee/payRecordManageDtl', '/platform/fee/payRecordManageDtl2']
            if(whiteList.includes(this.$route.path)) {
                return false
            }
            return !this.$route.path.includes('etail')
        },
        // 最终渲染的菜单
        visibleMenu () {
            let visibleMenu = this.menu.filter(item => (item.show && this.hasPermission(item.role)))
            visibleMenu.forEach(item => {
                if(item?.children.length > 0) {
                    item.children = item.children.filter(child => (child.show && this.hasPermission(child.role)))
                }
            })
            return visibleMenu
        },
        ...mapState(['userInfo', 'steps'])
    },
    data () {
        return {
            defaultActive: '1',
            showMenu: true,
            images: {
                gear: require('@/assets/images/zbgl.png'),
                gear2: require('@/assets/images/ershou.png'),
                rental: require('@/assets/images/zlgl.png'),
                rentalActive: require('@/assets/images/zlgl2.png'),
                repair: require('@/assets/images/wxbyfw.png'),
                repairActive: require('@/assets/images/wxbyfw2.png'),
                order: require('@/assets/images/ddgl.png'),
                orderActive: require('@/assets/images/ddgl2.png'),
                backTitle: require('@/assets/images/logodpgl.png'),
                dot: require('@/assets/images/dot219.png'),
                arrow: require('@/assets/images/tragle829.png')
            },
            currentSteps: [
                { description: '', },
                { description: '', },
            ],
            submenuName: '',
            menu: [
                {
                    menuName: '我的消息',
                    menuId: '7',
                    show: true,
                    children: [
                        { show: true, menuName: '发件箱', menuId: '7-1', route: { path: '/platform/mail/outBox', show: true, } },
                        { show: true, menuName: '收件箱', menuId: '7-2', route: { path: '/platform/mail/inBox', show: true,  } },
                        { show: true, menuName: '反馈中心', menuId: '7-3', route: { path: '/platform/mail/message' } },
                    ],
                },
                {
                    menuName: '信息内容管理',
                    menuId: '1',
                    show: true,
                    children: [
                        { show: true, menuName: '新闻 ', menuId: '1-1', route: { path: '/platform/content/news', query: { programaKey: 'aboutUs' }, } },
                        { show: true, menuName: '广告图', menuId: '1-2', route: { path: '/platform/content/adPicture', } },
                        { show: true, menuName: '友情链接', menuId: '1-3',  route: { path: '/platform/content/links', } },
                        { show: true, menuName: '我要开店', menuId: '1-5', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'becomeAM' }, } },
                        { show: true, menuName: '公司简介', menuId: '1-6', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'companyProfile' }, } },
                        { show: true, menuName: '联系我们', menuId: '1-7', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'contactUs' }, } },
                        { show: true, menuName: '慧采商城开店协议', menuId: '1-8', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'becomeSeller' }, } },
                        { show: true, menuName: '个人注册协议', menuId: '1-9', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'userRegistration' }, } },
                        { show: true, menuName: '个体户注册协议', menuId: '1-10', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'individualRegistration' }, } },
                        { show: true, menuName: '企业注册协议', menuId: '1-11', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'companyRegistration' }, } },
                        // { show: true, menuName: '找回密码', menuId: '1-9', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'retrievePassword' }, } },
                        { show: true, menuName: '市场合作', menuId: '1-12', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'marketCooperation' }, } },
                        { show: true, menuName: '用户协议', menuId: '1-13', route: { path: '/platform/content/richContent', query: { programaKey: 'userAgreement' }, } },
                        { show: true, menuName: '服务规则', menuId: '1-14', route: { path: '/platform/content/richContent', query: { programaKey: 'thePSR' }, } },
                        { show: true, menuName: '服务保障', menuId: '1-15', route: { path: '/platform/content/richContent', query: { programaKey: 'serviceGuarantee' }, } },
                        { show: true, menuName: '诚信管理', menuId: '1-16', route: { path: '/platform/content/richContent', query: { programaKey: 'integrityManagement' }, } },
                        { show: true, menuName: '常见问题', menuId: '1-17', route: { path: '/platform/content/richContent', query: { programaKey: 'commonProblem' }, } },
                        { show: true, menuName: '新手服务', menuId: '1-18', route: { path: '/platform/content/richContent', query: { programaKey: 'noviceService' }, } },
                        { show: true, menuName: '内部用户协议', menuId: '1-19', route: { path: '/platform/content/richContentDetail', query: { programaKey: 'internalUserProtocol' }, } },
                        { show: true, menuName: '临购商品价格公示', menuId: '1-20', route: { path: '/platform/content/news', query: { programaKey: 'LcPriceAnnouncement' }, } },
                        { show: true, menuName: '公告', menuId: '1-21', route: { path: '/platform/content/notification', query: { programaKey: 'notification' }, } },

                    ],
                },
                {
                    menuName: '商品内容管理',
                    menuId: '9',
                    show: true,
                    children: [
                        { show: true, menuName: '楼层栏目管理', menuId: '9-1', route: { path: '/platform/floor/floorColumn', } },
                        { show: true, menuName: '楼层管理', menuId: '9-2', route: { path: '/platform/floor/floorManager', } },
                    ],
                },
                {
                    menuName: '商品管理',
                    menuId: '2-',
                    show: true,
                    children: [
                        { show: true, menuName: '商品分类管理', menuId: '2-1', route: { path: '/platform/product/productCategory', } },
                        // { show: true, menuName: '商品基础库管理', menuId: '2-2', route: { path: '/platform/product/productBasics', } },
                        { show: true, menuName: '商品基础库管理', menuId: '2-2', route: { path: '/platform/product/productStorage', } },
                        { show: true, menuName: '店铺商品管理', menuId: '2-3', route: { path: '/platform/product/shopMaterialManage', } },
                        { show: false, menuName: '店铺审核商品管理', menuId: '2-4', route: { path: '/platform/product/shopCheckMaterial', } },
                        { show: true, menuName: '品牌管理', menuId: '2-5', route: { path: '/platform/brand/brandLogo', } },
                        { show: false, menuName: '店铺临购商品管理', menuId: '2-6', route: { path: '/platform/product/shopLcMaterialManage', } },
                        { show: false, menuName: '店铺审核临购商品管理', menuId: '2-7', route: { path: '/platform/product/shopCheckLcMaterial', } },
                        { show: true, menuName: '商品信息数据库', menuId: '2-8', route: { path: '/platform/product/productDatabase', } },

                    ],
                },
                {
                    menuName: '商铺管理',
                    menuId: '3-',
                    show: true,
                    children: [
                        { show: true, menuName: '商铺审核', menuId: '3-2', route: { path: '/platform/shop/shopAudit', } },
                        { show: true, menuName: '商铺管理', menuId: '3-1', route: { path: '/platform/shop/shopManage', } },
                        { show: true, menuName: '自营店管理', menuId: '3-3', route: { path: '/platform/shop/shopBusiness', } },
                    ],
                },
                {
                    menuName: '供应商管理',
                    menuId: '4-',
                    show: true,
                    children: [
                        { show: true, menuName: '供应商账号审核', menuId: '4-1', route: { path: '/platform/supplier/supplierAudit', } },
                        { show: true, menuName: '供应商查询', menuId: '4-2', route: { path: '/platform/supplier/supplierInquire', } },
                        { show: true, menuName: '供应商计费管理', menuId: '4-3', route: { path: '/platform/fee/payRecordManage', } },
                        { show: true, menuName: '费用明细', menuId: '4-4', route: { path: '/platform/fee/freeDtlAll', } },
                        { show: true, menuName: '交易统计', menuId: '4-5', route: { path: '/platform/platformDealfee', } },
                        { show: true, menuName: '供应商台账', menuId: '4-6', route: { path: '/platform/supplier/ledger', } },
                    ],
                },
                {
                    menuName: '采购方管理',
                    menuId: '20-',
                    show: true,
                    children: [
                        { show: true, menuName: '采购员台账', menuId: '20-1', route: { path: '/platform/purchaser/ledger', } },
                    ],
                },
                // {
                //     menuName: '缴费管理',
                //     menuId: '23-',
                //     show: this.showDevFunc,
                //     children: [
                //         { show: true, menuName: '年度服务费审核', menuId: '23-1', route: { path: '/platform/fee/yearPayRecord', } },
                //         { show: true, menuName: '交易服务费审核', menuId: '23-2', route: { path: '/platform/fee/dealPayRecord', } },
                //         { show: true, menuName: '年度服务费记录', menuId: '23-4', route: { path: '/platform/fee/year', } },
                //         { show: true, menuName: '交易服务费记录', menuId: '23-3', route: { path: '/platform/platformDealfee', } },
                //     ],
                // },
                {
                    menuName: '订单管理',
                    menuId: '5-',
                    show: true,
                    children: [
                        { show: true, menuName: '订单查询', menuId: '5-1', route: { path: '/platform/order/searchOrder', } },
                        // { show: true, menuName: '路桥结算订单', menuId: '5-2', route: { path: '/platform/order/searchOrder', } },
                        // { show: true, menuName: '未完成订单', menuId: '5-3', route: { path: '/platform/order/searchOrder', } },
                        { show: true, menuName: '零星采购退货查询', menuId: '5-4', route: { path: '/platform/order/refund', } },
                        { show: false, menuName: '大宗月供退货查询', menuId: '5-6', route: { path: '/platform/order/tallRefund', } },
                        { show: true, menuName: '大宗临购退货查询', menuId: '5-7', route: { path: '/platform/order/blockIndex', } },
                    ],
                },
                {
                    menuName: '发票管理',
                    menuId: '11-',
                    show: true && this.showDevFunc,
                    children: [
                        { show: true && this.showDevFunc, menuName: '发票记录', menuId: '11-4', route: { path: '/platform/order/invoice', } },
                    ],
                },
                {
                    menuName: '客户管理',
                    menuId: '44',
                    show: true,
                    children: [
                        { show: true, menuName: '账号查询', menuId: '44-1', route: { path: '/platform/user/userManager', } },
                        { show: true, menuName: '内部企业查询', menuId: '44-2', route: { path: '/platform/enterprise/inEnterpriseManager', } },
                        { show: true, menuName: '外部企业查询', menuId: '44-3', route: { path: '/platform/enterprise/outEnterpriseManager', } },
                        // { show: true, menuName: '供应商企业查询', menuId: '5-3', route: { path: '/platform/enterprise/inEnterpriseManager', } }
                    ],
                },
                // {
                //     menuName: '竞价管理',
                //     menuId: '22',
                //     show: true,
                //     children: [
                //         { show: true, menuName: '提交审核', menuId: '22-1', route: { path: '/platform/bidding/submitAudit', } },
                //         { show: true, menuName: '竞价列表', menuId: '22-2', route: { path: '/platform/bidding/biddingList', } },
                //     ],
                // },
                {
                    menuName: '统计分析',
                    menuId: '6',
                    role: ['平台运营统计查看权限'],
                    show: true,
                    children: [
                        { show: true, menuName: '订单统计', menuId: '6-1', route: { path: '/platform/analysis/order', } },
                        { show: true, menuName: '商品统计', menuId: '6-2', route: { path: '/platform/analysis/product', } },
                        { show: true, menuName: '店铺统计', menuId: '6-3', route: { path: '/platform/analysis/shop', } },
                        { show: true, menuName: '用户统计', menuId: '6-4', route: { path: '/platform/analysis/customer', } },
                        {
                            show: true,
                            menuName: '系统运营统计',
                            menuId: '6-5',
                            route: { path: '/platform/analysis/system' },
                        },
                        { show: true, menuName: '商品操作数统计', menuId: '6-6', route: { path: '/platform/analysis/operand', } },
                    ],
                },
                {
                    menuName: '报表管理',
                    menuId: '15',
                    show: true,
                    children: [
                        { show: true, menuName: '上架商品报表', menuId: '15-1', route: { path: '/platform/analysis/productFrom', } },
                        { show: true, menuName: '物资结算报表', menuId: '15-2', route: { path: '/platform/analysis/orderStatement', } },
                        { show: true, menuName: '物资交易量报表', menuId: '15-3', route: { path: '/platform/analysis/sendProductStatement', } },
                        { show: true, menuName: '物资对账统计台账单', menuId: '15-5', route: { path: '/platform/analysis/reconciliationLedger', } },
                        // { show: true, menuName: '供应商费用报表', menuId: '15-4', route: { path: '/platform/analysis/supplierFree', } },
                    ],
                },
                {
                    menuName: '系统管理',
                    menuId: '8',
                    show: true,
                    children: [
                        // { show: true, menuName: '菜单管理', menuId: '8-3', route: { path: '/platform/system/menuManage', query: { key: 'params' } } },
                        // { show: true, menuName: '用户管理', menuId: '8-4', route: { path: '/platform/system/userManage', query: { key: 'params' } } },
                        // { show: true, menuName: '角色管理', menuId: '8-5', route: { path: '/platform/system/roleManage', query: { key: 'params' } } },
                        { show: true, menuName: '参数管理', menuId: '8-1', route: { path: '/platform/system/systemParam', query: { key: 'params' } } },
                        { show: true, menuName: '字典管理', menuId: '8-2', route: { path: '/platform/system/dictionaries', query: { key: 'dictionaries' } } },
                        // { show: true, menuName: '蜀道企业库管理', menuId: '8-3', route: { path: '/platform/system/shudaoEnterprise', query: { key: 'shudaoEnterprise' }, } },
                    ],
                },
                {
                    menuName: '流程管理',
                    menuId: '10',
                    show: true,
                    children: [
                        { show: true, menuName: '审核场景审核流程', menuId: '10-1', route: { path: '/platform/process/review', query: { key: 'params' } } },
                    ],
                },
            ],
            idList: [],
        }
    },
    methods: {
        ...mapActions(['changeSteps']),
        // 设置默认高亮的菜单项
        setDefaultActive () {
            let path = this.$route.path
            this.visibleMenu.forEach(item => {
                if(item.route && item.route.path === path) {
                    this.currentSteps = [{ description: item.menuName }]
                    this.defaultActive = item.menuId
                }
                if(!item.children) return
                item.children.forEach(subItem => {
                    let childPath = subItem.route.path
                    if(childPath === '/platform/content/richContent') {
                        path = this.$route.fullPath
                        childPath = `${childPath}?programaKey=${subItem.route.query['programaKey']}`
                    }
                    if(childPath === '/platform/content/richContentDetail') {
                        path = this.$route.fullPath
                        childPath = `${childPath}?programaKey=${subItem.route.query['programaKey']}`
                    }
                    if (childPath !== path) return
                    this.defaultActive = subItem.menuId
                    this.submenuName = item.menuName
                    this.currentSteps[0].description = item.menuName
                    this.currentSteps[1].description = subItem.menuName
                })
            })
        },
        // 传入权限字段，判断用户是否有此权限
        hasPermission (permission) {
            if(!permission || permission.length === 0) return true
            let { roles } = this.userInfo
            let hasPermission = true
            permission.forEach(item => {
                if(!hasPermission) return
                hasPermission = (roles && roles.includes(item)) || this.userInfo[item] === 1
            })
            return hasPermission
        },
        // 展开子菜单
        handleSubOpen (key) {
            this.menu.forEach(item => item.menuId === key ? this.submenuName = item.menuName : null)
        },
        // 修改路径显示
        changePath (num, name) {
            if(this.currentSteps.length === 1) {
                num === 0 ? this.currentSteps[0].description = name : this.currentSteps.push({ description: name })
            }else{
                if(num === 0) {
                    this.currentSteps.pop()
                    this.currentSteps[0].description = name
                }else{
                    this.currentSteps[0].description = this.submenuName
                    this.currentSteps[1].description = name
                }
            }
            this.changeSteps(this.currentSteps)
        },
    },
    async created () {
    },
    mounted () {
    },
}
</script>
<style scoped lang="scss">
@import '../../assets/css/backStage.css';
/deep/ .el-dialog__header {
    background: url(../../assets/test.png);
}
.page {
    height: 100%;
    display: flex;
    font-family: 'SourceHanSansCN-Regular';
}
.table-box {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    box-sizing: border-box;
    background-color: #eff2f6;
    &::-webkit-scrollbar{display: none;}

    .history {
        font-weight: bold;
        font-size: 17px;
        &>div {
            line-height: 84px;

            span {color: gray;}
        }
    }

    .router-box {
        height: 100%;
        display: flex;
        flex-direction: column;

        &>*:last-child {flex-grow: 1;}
    }
}
</style>
