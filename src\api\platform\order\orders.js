import service from '@/utils/request'
const { httpPost, httpGet } = service

const orderList = params => {
    return httpPost({
        url: '/materialMall/platform/orders/listByParameters',
        params
    })
}
const selectOrderList = params => {
    return httpPost({
        url: '/materialMall/platform/orders/selectOrderList',
        params
    })
}

const orderItemList = params => {
    return httpPost({
        url: '/materialMall/platform/orderItem/listByParameters',
        params
    })
}
const batchCreateTwoOrder = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/batchCreateTwoOrder',
        params
    })
}
const batchCreateContractTwoOrder = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/batchCreateContractTwoOrder',
        params
    })
}
const batchCreateLinXingTwoOrder = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/batchCreateLinXingTwoOrder',
        params
    })
}
const createBidingByOrder = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/createBidingByOrder',
        params
    })
}
const createBidingByTemporary = params => {
    return httpPost({
        url: '/materialMall/supplier/synthesizeTemporary/createBidding',
        params
    })
}
const updateToBidingState = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/updateToBidingState',
        params
    })
}
const updateNotUseState = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/updateNotUseState',
        params
    })
}
const getOrderInfoOutPutPdf = params => {
    return httpGet({
        url: '/materialMall/shopManage/orders/getOrderInfoOutPutPdf',
        params,
        responseType: 'blob'
    })
}
const orderShipList = params => {
    return httpPost({
        url: '/materialMall/orderShip/listByEntity',
        params
    })
}
/**
 * 多供方发货单查看
 * @param params
 * @returns {*}
 */
const orderTwoShipList = params => {
    return httpPost({
        url: '/materialMall/orderShip/listByTwoEntity',
        params
    })
}
const updateBatch = params => {
    return httpPost({
        url: '/materialMall/platform/orders/updateBatch',
        params
    })
}

// 店铺修改订单
const addOrderSupplier = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/addOrderSupplier',
        params
    })
}
const closeOrderSupplier = params => {
    return httpGet({
        url: '/materialMall/shopManage/orders/closeOrderSupplier',
        params
    })
}
const masterAffirmTwoOrder = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/masterAffirmTwoOrder',
        params
    })
}

const batchUpdateTwoOrderPrice = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/batchUpdateTwoOrderPrice',
        params
    })
}

const shopManageOrderList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listByParameters',
        params
    })
}

const listByEntitySynthesizeTemporary = params => {
    return httpPost({
        url: '/materialMall/supplier/synthesizeTemporary/listByEntity',
        params
    })
}
const platformListByEntityST = params => {
    return httpPost({
        url: '/materialMall/platform/synthesizeTemporary/platformListByEntity',
        params
    })
}
const listContractTwoOrderList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listContractTwoOrderList',
        params
    })
}

const listByEntity = params => {
    return httpPost({
        url: '/materialMall/shopManage/dealOrderInfo/listByEntity',
        params
    })
}

// const shopListShipByAffirmList = params => {
//     return httpPost({
//         url: '/materialMall/orderShipDtl/shopManage/listShipByAffirmList',
//         params
//     })
// }

const shopListShipByAffirmList = params => {
    return httpPost({
        url: '/materialMall/shopManage/dealOrderInfo/listByEntity',
        params
    })
}
const outputExcel = params => {
    return httpPost({
        url: '/materialMall/shopManage/dealOrderInfo/outputExcel',
        params,
        responseType: 'blob'
    })
}
/**
 * 店铺交易量导出
 * @param params
 * @returns {*}
 */
const shopOutputExcel = params => {
    return httpPost({
        url: '/materialMall/orderShipDtl/shopManage/outputExcel',
        params,
        responseType: 'blob'
    })
}
// 平台
const platformListByEntity = params => {
    return httpPost({
        url: '/materialMall/platform/dealOrderInfo/listByEntity',
        params
    })
}

const listShipByAffirmList = params => {
    return httpPost({
        url: '/materialMall/orderShipDtl/platform/listShipByAffirmList',
        params
    })
}
const listShipByAffirmOrgList = params => {
    return httpPost({
        url: '/materialMall/orderShipDtl/platform/listShipByAffirmOrgList',
        params
    })
}
const platformOutputExcel = params => {
    return httpPost({
        url: '/materialMall/platform/dealOrderInfo/outputExcel',
        params,
        responseType: 'blob'
    })
}
const excelTotalCountFree = params => {
    return httpPost({
        url: '/materialMall/platformYearFee/platform/excelTotalCountFree',
        params,
        responseType: 'blob'
    })
}
const supplyOutputExcel = params => {
    return httpPost({
        url: '/materialMall/platform/dealOrderInfo/supplyOutputExcel',
        params,
        responseType: 'blob'
    })
}

const platformOutputShipExcel = params => {
    return httpPost({
        url: '/materialMall/orderShipDtl/platform/outputExcel',
        params,
        responseType: 'blob'
    })
}
const getTwoOrderList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/getTwoOrderList',
        params
    })
}

const affirmTwoOrder = params => {
    return httpGet({
        url: '/materialMall/shopManage/orders/affirmTwoOrder',
        params
    })
}
// 拆单查询供应商列表
const listSupplierByShopId = params => {
    return httpPost({
        url: '/materialMall/shopSupplierRele/listByShopId',
        params
    })
}
//零星采购订单
const shopManagePurchaseOrderList = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/listByParameters',
        params
    })
}
// 订单统计 （后台）
const getPlatformOrdersCount = params => {
    return httpPost({
        url: '/materialMall/orders/getPlatformOrdersCount',
        params
    })
}
// 订单统计 物资名称维度（后台）
const getAssetNameDimensionList = params => {
    return httpPost({
        url: '/materialMall/orderItem/getAssetNameDimensionList',
        params
    })
}
// 订单统计 （供应商）
const getSupplierPlatformOrdersCount = params => {
    return httpPost({
        url: '/materialMall/orders/getSupplierPlatformOrdersCount',
        params
    })
}

// 订单项统计
const getPlatformOrdersItemCount = params => {
    return httpPost({
        url: '/materialMall/orderItem/getPlatformOrderItemCount',
        params
    })
}
/**
 * 根据订单集合生成发货单
 * @param params
 * @returns {*}
 */
const createShipBatch = params => {
    return httpPost({
        url: '/materialMall/orderShip/createBatchByOrderItem',
        params
    })
}
const orderCloseClick = params => {
    return httpGet({
        url: '/materialMall/shopManage/orders/orderCloseClick',
        params
    })
}
// 接单
const acceptOrder = (id, assigneeId) => {
    return service.post(`/materialMall/shopManage/orders/accept/${id}`, {}, { params: { assigneeId } })
}
// 获取有接单权限的人
const getAcceptors = () => {
    return service.get('/materialMall/shopManage/orders/accept/users')
}
// 退单
const rejectOrder = (id, reason) => {
    return service.post(`/materialMall/shopManage/orders/reject/${id}`, { reason })
}

export {
    batchCreateTwoOrder,
    platformListByEntity,
    orderCloseClick,
    platformOutputExcel,
    orderList,
    orderItemList,
    updateBatch,
    shopManageOrderList,
    getPlatformOrdersCount,
    getSupplierPlatformOrdersCount,
    createShipBatch,
    orderShipList,
    shopManagePurchaseOrderList,
    closeOrderSupplier,
    listSupplierByShopId,
    addOrderSupplier,
    getTwoOrderList,
    affirmTwoOrder,
    masterAffirmTwoOrder,
    batchUpdateTwoOrderPrice,
    listByEntity,
    createBidingByOrder,
    outputExcel,
    listShipByAffirmList,
    shopListShipByAffirmList,
    getOrderInfoOutPutPdf,
    shopOutputExcel,
    platformOutputShipExcel,
    listByEntitySynthesizeTemporary,
    listContractTwoOrderList,
    orderTwoShipList,
    updateToBidingState,
    updateNotUseState,
    supplyOutputExcel,
    selectOrderList,
    platformListByEntityST,
    excelTotalCountFree,
    batchCreateContractTwoOrder,
    batchCreateLinXingTwoOrder,
    getPlatformOrdersItemCount,
    createBidingByTemporary,
    getAssetNameDimensionList,
    acceptOrder,
    getAcceptors,
    rejectOrder,
    listShipByAffirmOrgList
}