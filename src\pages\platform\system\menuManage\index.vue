<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <div>
            <div class="left">
              <div class="left-btn">
                  <el-button type="primary" @click="operate_td('新增')" class="btn-greenYellow">新增</el-button>
              </div>
            </div>
          </div>
          <!-- 新增按钮 -->
          <div class="search_box">
            <el-input type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
          </div>
        </div>
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table elTtreeTable" :style="{ width: '100%' }">
         <el-table :data="tableData" :height="rightTableHeight"  style="width: 100%;margin-bottom: 20px;" row-key="id"
            border default-expand-all :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
            <el-table-column label="操作" width="300">
                <template slot-scope="scope">
                    <span class="action" @click="operate_td('新增下级', scope.row)" v-if="scope.row.menuType == '1'">新增下级</span>
                    <span class="action" @click="operate_td('编辑', scope.row)">编辑</span>
                    <span class="action" @click="operate_td('删除', scope.row)">删除</span>
                </template>
            </el-table-column>
            <el-table-column label="菜单名称" prop="menuName"/>
            <el-table-column label="排序" prop="sort" width="200" sortable/>
            <el-table-column label="菜单类型" prop="menuType" width="200">
                <template slot-scope="scope">
                    <span v-if="scope.row.menuType == '1'">目录</span>
                    <span v-else-if="scope.row.menuType == '2'">菜单</span>
                    <span v-else-if="scope.row.menuType == '3'">按钮</span>
                </template>
            </el-table-column>
            <el-table-column label="链接地址" prop="menuPath"/>
        </el-table>
      </div>
    </div>
    <el-dialog v-dialogDrag :title="menuModal.title" :visible.sync="menuModal.visible" width="40%" :close-on-click-modal="false">
      <div class="e-form" style="padding: 0 10px 10px;">
        <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="系统名称：" prop="systemVal">
                  <el-select v-model="formData.systemVal" @change="changeSystem" placeholder="请选择系统名称">
                    <el-option v-for="item in systemOptions" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                  </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="上级菜单：" prop="prevLevel">
                  <el-select v-model="formData.prevLevel" placeholder="请选择上级菜单">
                    <el-option v-for="item in prevOptions" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                  </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="菜单类型：" prop="menuType">
                <el-radio-group v-model="formData.menuType">
                    <el-radio :label="'1'">目录</el-radio>
                    <el-radio :label="'2'">菜单</el-radio>
                    <el-radio :label="'3'">按钮</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="菜单名称：" prop="menuName">
                <el-input v-model="formData.menuName" placeholder="请输入菜单名称"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="排序：" prop="sort">
                <el-input v-model="formData.sort" placeholder="请输入排序"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
          <el-col :span="24">
            <el-form-item label="链接地址：" prop="menuPath">
              <el-input v-model="formData.menuPath" placeholder="请输入链接地址"></el-input>
            </el-form-item>
          </el-col>
          </el-row>
          <div class="buttons">
            <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
            <el-button @click="menuModal.visible = false">取消</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { update, createSysParam, del } from '@/api/platform/system/systemParam'
// eslint-disable-next-line no-unused-vars
import { debounce, showLoading, hideLoading } from '@/utils/common'
export default {
    components: {},
    watch: {},
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291 + 60
        },
    },
    data () {
        return {
            keywords: '',
            tableData: [],
            screenWidth: 0,
            screenHeight: 0,
            requestParams: {},
            menuModal: {
                visible: false, title: '',
            },
            systemOptions: [],
            prevOptions: [],
            formData: {
                systemVal: undefined,
                prevLevel: undefined,
                menuType: '1',
                menuName: '',
                sort: '',
                menuPath: '',
            },
            formRules: {
                systemVal: [{ required: true, message: '请选择系统名称', trigger: 'change' }],
                // prevLevel: [{ required: true, message: '请选择上级菜单', trigger: 'change' }],
                menuName: [
                    { required: true, message: '请输入菜单名称', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { max: 10, message: '菜单名称长度不能超过10个字符', trigger: 'blur' }
                ],
                sort: [
                    { required: true, message: '请输入排序', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { max: 3, message: '排序长度不能超过3个字符', trigger: 'blur' }
                    // { pattern: new RegExp(/^[\w\W]{0,5}$/), message: '长度不超过5', trigger: 'blur' },
                    // { validator: this.publicFunc.formFunc.isPositiveIntInput }
                ],
                menuPath: [
                    { required: true, message: '请输入链接地址', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { max: 32, message: '链接地址长度不能超过32个字符', trigger: 'blur' }
                ],
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        this.getTableData()
    },
    created () {
        let res = [
            { value: '1', label: '后台管理平台' },
            { value: '2', label: '供应商履约平台' },
            { value: '3', label: '采购人履约平台' }
        ]
        this.systemOptions = res
    },
    methods: {
        changeSystem () {
            let res = [
                { value: '1', label: '商品管理' },
                { value: '2', label: '系统管理' },
            ]
            res.unshift({ label: '无' })
            this.prevOptions = res
            this.formData.prevLevel = undefined
        },
        // 删除、编辑、新增
        operate_td (title, row ) {
            if(title === '删除') {
                this.clientPop('info', '您确定要删除该信息吗？', async () => {
                    showLoading()
                    del({ id: row.systemId }).then(res => {
                        if(res.message === '操作成功') {
                            /*this.clientPop('suc', '删除成功', () => {
                                this.onSearch()
                            })*/
                            this.$message.success('删除成功')
                            this.onSearch()
                        }else{
                            this.clientPop('warn', res.message, () => {})
                        }
                        hideLoading()
                    })
                    hideLoading()
                })
            }else if (title == '新增下级') {
                this.formData.systemVal = row.systemVal
                this.formData.menuName = ''
                this.formData.menuType = '1'
                this.formData.sort = ''
                this.formData.menuPath = ''
                this.changeSystem()
                this.formData.prevLevel = row.level
                this.menuModal.title = '新增菜单'
                this.menuModal.visible = true
                this.$refs.formEdit.clearValidate()
            }else if (title == '编辑') {
                this.formData.systemVal = row.systemVal
                this.formData.menuName = row.menuName
                this.formData.menuType = row.menuType
                this.formData.sort = row.sort
                this.formData.menuPath = row.menuPath
                this.formData.level = row.level
                this.formData.prevLevel = row.prevLevel
                this.changeSystem()
                this.menuModal.title = '编辑菜单'
                this.menuModal.visible = true
                this.$refs.formEdit.clearValidate()
            }else if (title == '新增') {
                this.formData.systemVal = undefined
                this.formData.prevLevel = undefined
                this.formData.menuName = ''
                this.formData.menuType = '1'
                this.formData.sort = ''
                this.formData.menuPath = ''
                this.menuModal.title = '新增菜单'
                this.menuModal.visible = true
                this.$refs.formEdit.clearValidate()
            }
        },
        // 获取列表数据
        async getTableData () {
            this.requestParams = {
                keywords: this.keywords
            }
            let res = [
                { id: 1, menuName: '后台管理平台', systemVal: '1', level: undefined, prevLevel: undefined, sort: 1, menuType: '1', menuPath: '/usermang', children: [
                    { id: 11, menuName: '系统管理', systemVal: '1', level: '2', prevLevel: undefined, sort: 1, menuType: '1', menuPath: '/usermang', children: [
                        { id: 111, menuName: '用户管理', systemVal: '1', prevLevel: '2', sort: 1, menuType: '2', menuPath: '/userManage' },
                        { id: 112, menuName: '用户管理新增', systemVal: '1', prevLevel: '2', sort: 2, menuType: '3', menuPath: '/userManageAdd' },
                        { id: 113, menuName: '角色管理', systemVal: '1', prevLevel: '2', sort: 3, menuType: '2', menuPath: '/roleManage' },
                    ] },
                    { id: 12, menuName: '商品管理', systemVal: '1', level: '1', prevLevel: undefined, sort: 2, menuType: '1', menuPath: '/usermang', children: [
                        { id: 121, menuName: '商品分类管理', systemVal: '1', prevLevel: '1', sort: 1, menuType: '2', menuPath: '/productManage' },
                        { id: 122, menuName: '商品分类管理新增', systemVal: '1', prevLevel: '1', sort: 2, menuType: '3', menuPath: '/productManageAdd' }
                    ] },
                ] },
                { id: 2, menuName: '供应商履约后台', systemVal: '2', level: undefined, prevLevel: undefined, sort: 2, menuType: '1', menuPath: '/usermang', children: [
                    { id: 21, menuName: '系统管理', systemVal: '2', level: '1', prevLevel: undefined, sort: 1, menuType: '1', menuPath: '/usermang', children: [
                        { id: 211, menuName: '用户管理', systemVal: '2', prevLevel: '1', sort: 1, menuType: '2', menuPath: '/userManage' },
                        { id: 212, menuName: '用户管理新增', systemVal: '2', prevLevel: '1', sort: 2, menuType: '3', menuPath: '/userManageAdd' },
                        { id: 213, menuName: '角色管理', systemVal: '2', prevLevel: '1', sort: 3, menuType: '2', menuPath: '/roleManage' },
                    ] },
                    { id: 22, menuName: '商品管理', systemVal: '2', level: '2', prevLevel: undefined, sort: 2, menuType: '1', menuPath: '/usermang', children: [
                        { id: 221, menuName: '商品分类管理', systemVal: '2', prevLevel: '2', sort: 1, menuType: '2', menuPath: '/productManage' },
                        { id: 222, menuName: '商品分类管理新增', systemVal: '2', prevLevel: '2', sort: 2, menuType: '3', menuPath: '/productManageAdd' }
                    ] },
                ] },
                { id: 3, menuName: '采购人履约平台', systemVal: '3', level: undefined, prevLevel: undefined, sort: 3, menuType: '1', menuPath: '/usermang', children: [
                    { id: 32, menuName: '商品管理', systemVal: '3', level: '1', prevLevel: undefined, sort: 2, menuType: '1', menuPath: '/usermang', children: [
                        { id: 321, menuName: '商品分类管理', systemVal: '3', prevLevel: '1', sort: 1, menuType: '2', menuPath: '/productManage' },
                        { id: 322, menuName: '商品分类管理新增', systemVal: '3', prevLevel: '1', sort: 2, menuType: '3', menuPath: '/productManageAdd' }
                    ] },
                    { id: 31, menuName: '系统管理', systemVal: '3', level: '2', prevLevel: undefined, sort: 1, menuType: '1', menuPath: '/usermang', children: [
                        { id: 311, menuName: '用户管理', systemVal: '3', prevLevel: '2', sort: 1, menuType: '2', menuPath: '/userManage' },
                        { id: 312, menuName: '用户管理新增', systemVal: '3', prevLevel: '2', sort: 2, menuType: '3', menuPath: '/userManageAdd' },
                        { id: 313, menuName: '角色管理', systemVal: '3', prevLevel: '2', sort: 3, menuType: '2', menuPath: '/roleManage' },
                    ] },
                ] },
            ]
            this.tableData = res
        },
        // 关键词搜索
        onSearch () {
            this.getTableData()
        },
        // 保存新增/修改
        onSave () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        if (this.menuModal.title === '编辑菜单') {
                            return this.handleEditData()
                        }
                        this.handleCreateData()
                    })
                }
            })
        },
        // 修改数据
        handleEditData () {
            update(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                }
            })
        },
        // 保存数据
        handleCreateData () {
            createSysParam(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                }
            })
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.right .top {padding-right: 10px}
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}
/deep/ .el-dialog .el-dialog__body {margin: 0;height: auto;}
/deep/ .e-form .buttons {position: relative;background: transparent;}

/deep/ .e-form .el-form-item .el-form-item__explain {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}
.elTtreeTable /deep/ {
    .sort-caret.ascending {top: 5px;}
    .sort-caret.descending {display: inline-block;}
}
</style>
