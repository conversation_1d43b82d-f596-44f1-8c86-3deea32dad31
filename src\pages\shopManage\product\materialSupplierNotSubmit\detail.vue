<template><!-- 二级供应商新增自营店铺零星采购商品 -->
    <div class="e-form">
        <BillTop @cancel="handleClose"></BillTop>
        <div v-loading='formLoading' class="tabs warningTabs" style="padding-top: 70px;">
            <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="物资" name="baseInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <div id="tabs-content">
                    <div id="baseInfCon" class="con">
                        <div class="tabs-title" id="baseInfo">物资详情</div>
                        <!--                        新增-->
                        <div style="width: 100%" class="form" v-if="showForm">
                            <el-form
                                :model="addForm.formData" :rules="formRules" label-width="200px" ref="formEdit"
                                :disabled="false" class="demo-ruleForm"
                            >
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="物资名称：" prop="relevanceName">
                                    <el-input
                                      placeholder="清选择物资" clearable disabled
                                      v-model="addForm.formData.relevanceName"
                                    ></el-input>
                                    <el-button size="mini"  :disabled="viewType != 'add'" type="primary" @click="importDeviceSelect">选择
                                    </el-button>
                                  </el-form-item>
                                </el-col>
                                <el-col :span="12" v-show="rowData.viewType != 'add'&&addForm.formData.state!=4">
                                  <el-form-item label="商品编码：" prop="skuName">
                                    <el-input disabled
                                              placeholder="请输入商品编码" clearable v-model="addForm.formData.serialNum"
                                    ></el-input>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="分类：" prop="classId">
<!--                                              :disabled="viewType != 'add'&&addForm.formData.state!=4"-->
                                            <category-cascader
                                                customStyle="width: 100%"
                                                style="width: 100%"
                                                :disabled="true"
                                                :classPath.sync="addForm.formData.classPath"
                                                :classId.sync='addForm.formData.classId'
                                                :catelogPath="addForm.formData.classPath"
                                                :productType="0"
                                                @change="resetRelevanceAndBrand"
                                            ></category-cascader>
                                        </el-form-item>
                                    </el-col>
                                    <!-- <el-col :span="12">
                                        <el-form-item label="名称：" prop="productName">
                                            <el-input
                                                placeholder="请输入名称" clearable
                                                v-model="addForm.formData.productName"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col> -->
                                    <el-col :span="12">
                                        <el-form-item label="税率（%）：" prop="taxRate">
                                            <el-input
                                                v-model="addForm.formData.taxRate"
                                                :step="0.01"
                                                type="number"
                                                placeholder="填写税率"
                                                @change="writeTaxRate"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="店铺：" prop="shopId">
                                            <el-input
                                                placeholder="请选择店铺" clearable disabled
                                                v-model="addForm.formData.shopName"
                                            ></el-input>
                                            <el-button size="mini" type="primary" @click="shopDialog">选择</el-button>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="品牌：" prop="brandName">
                                            <el-input
                                                clearable disabled placeholder="请选择品牌"
                                                v-model="addForm.formData.brandName"
                                            ></el-input>
                                            <el-button size="mini" type="primary" @click="brandDialog">选择</el-button>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="规格：" prop="skuName">
                                            <el-input
                                                placeholder="请输入规格" clearable v-model="addForm.formData.skuName"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <!-- <el-col :span="12">
                                        <el-form-item label="供货价" prop="costPrice">
                                            <el-input
                                                placeholder="请输入供货价" clearable type="number"
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                v-model="addForm.formData.costPrice"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col> -->
                                  <el-col :span="12">
                                    <el-form-item label="计量单位" prop="unit">
                                      <el-select
                                        filterable @change="numUnitChange" v-model="addForm.formData.unit"
                                        placeholder="请选择计量单位"
                                        :popper-append-to-body="false"
                                      >
                                        <el-option
                                          v-for="item in addForm.numUnitOptions" :key="item.value"
                                          :label="item.label" :value="item.label"
                                        >
                                        </el-option>
                                      </el-select>
                                    </el-form-item>
                                  </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="库存：" prop="stock">
                                            <el-input
                                                placeholder="请输入库存" clearable type="number"
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                v-model="addForm.formData.stock"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                  <el-col :span="12">
                                    <el-form-item label="材质：" prop="productTexture">
                                      <el-input
                                        v-model="addForm.formData.productTexture" clearable
                                        oninput="if(value.length > 16)value = value.slice(0, 16)"
                                        placeholder="请输入材质"
                                      ></el-input>
                                    </el-form-item>
                                  </el-col>
                                </el-row>
                                <!-- <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="原价：" prop="originalPrice">
                                            <el-input
                                                placeholder="请输入原价" clearable type="number"
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                v-model="addForm.formData.originalPrice"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="12" >
                                        <el-form-item label="销售价格" prop="sellPrice">
                                            <el-input
                                                placeholder="请输入销售价格" clearable type="number"
                                                oninput="if(value.length > 16)value = value.slice(0, 16)"
                                                v-model="addForm.formData.sellPrice"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row> -->
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="排序：">
                                            <el-input
                                                placeholder="请输入排序" clearable type="number"
                                                oninput="if(value.length > 10)value = value.slice(0, 10)"
                                                v-model="addForm.formData.shopSort"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col>
                                  <el-col :span="12">
                                    <el-form-item label="年化率（%）：" prop="annualizedRate">
                                      <el-input
                                        v-model="addForm.formData.annualizedRate" clearable
                                        placeholder="请输入年化率"
                                        type="number"
                                        :step="0.01"
                                        @change="arChange"
                                      ></el-input>
                                    </el-form-item>
                                  </el-col>
                                </el-row>
<!--                                <el-row>-->
                                    <!-- 自营店需要采购进价字段 -->
                                    <!-- <el-col :span="12">
                                        <el-form-item label="采购进价：" prop="purchasePrice">
                                            <el-input
                                                v-model="addForm.formData.purchasePrice" clearable
                                                placeholder="请输入采购进价"
                                            ></el-input>
                                        </el-form-item>
                                    </el-col> -->
<!--                                </el-row>-->
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="账期：" prop="accountPeriod">
                                            <el-select v-model="addForm.formData.accountPeriod" placeholder="请选择账期" multiple :popper-append-to-body="false">
                                              <el-option
                                                v-for="item in accountPeriodOptions"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                              </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                              <div v-if="addForm.formData.accountPeriod.length > 0">
                                <div class="tabContent">
                                  <div v-for="item in addForm.formData.accountPeriod" :key="item" class="accountPeriodTabs">
                                    <div :class="currentTab === item ? 'tabTitle' : 'tabTitleNoSelect'" @click="getCurrentTab(item)">{{item + "个月账期信息"}}</div>
                                  </div>
                                </div>
                                <el-row style="margin: 1% 0 1% 9%">
                                  <el-button type="primary" @click="addRegion" style="line-height: 20px;">新增区域</el-button>
                                </el-row>
                                <div style="margin: 10px 0;">
                                  <div>
                                    <div v-for="(item, index) in addForm.formData.currentRegionTableData" :key="item.index">
                                      <el-row>
                                        <el-col :span="12">
                                          <el-form-item :label="item.regionName">
                                            <div v-if="item.regionName === '全区域'" style="width: 100%">
                                              <el-select v-model="item.selectAddressOptionsAll" multiple placeholder="请选择" @change="handleAddressChange1" :popper-append-to-body="false">
                                                <el-option
                                                  v-for="item in economizeData"
                                                  :key="item.value"
                                                  :label="item.label"
                                                  :value="item.value"
                                                >
                                                </el-option>
                                              </el-select>
                                            </div>
                                            <div v-else style="width: 100%">
                                              <el-cascader
                                                style="width: 100%"
                                                v-model="item.selectAddressOptions"
                                                @change="handleAddressChange(item.index)"
                                                :options="marketData"
                                                :props="{ multiple: true, checkStrictly: true }"
                                                :append-to-body="false"
                                                clearable>
                                              </el-cascader>
                                            </div>
                                          </el-form-item>
                                        </el-col>
                                        <el-col :span="5">
                                          <el-form-item label="先款后货：" label-width="150px" :prop="'currentRegionTableData.' + index + '.payBeforeDelivery'" :rules="formRules.payBeforeDelivery">
                                            <el-input
                                              v-model="item.payBeforeDelivery"
                                              style="width: 100%"
                                              type="number"
                                              @change="payBeforeDeliveryChange(item.index)"
                                            ></el-input>
                                          </el-form-item>
                                        </el-col>
                                        <el-col :span="5">
                                          <el-form-item label="供货价（含税）：" :prop="'currentRegionTableData.' + index + '.taxInPrice'" :rules="formRules.taxInPrice" label-width="150px">
                                            <el-input
                                              v-model="item.taxInPrice"
                                              style="width: 100%"
                                              type="number"
                                              @change="taxInPriceChange(item.index)"
                                            ></el-input>
                                          </el-form-item>
                                        </el-col>
                                        <el-col :span="5">
                                          <el-form-item label="供货价（不含税）：" label-width="150px">
                                            <el-input
                                              v-model="item.price"
                                              style="width: 100%"
                                              type="number"
                                            ></el-input>
                                          </el-form-item>
                                        </el-col>
                                        <el-col :span="1">
                                          <el-form-item>
                                            <el-button icon="el-icon-delete" type="text" size="mini" @click="removeOneParamsData(item)"></el-button>
                                          </el-form-item>
                                        </el-col>
                                      </el-row>
                                    </div>
                                  </div>
                                </div>
                              </div>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            required class="uploader" label="物资主图（推荐654x490）：" prop="adminFile"
                                        >
                                            <el-upload
                                                :class="addForm.adminFileLength === 1 ? 'hide_box_admin' : ''"
                                                action="fakeaction" ref="adminFileRef"
                                                :file-list="addForm.formData.adminFile" list-type="picture-card"
                                                :before-upload="handleBeforeUpload" :auto-upload="false" :limit="1"
                                                :on-remove="adminFileRemove" :on-change="adminFileChange"
                                                :on-preview="handlePictureCardPreview"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            required class="uploader" label="物资图片（推荐654x490）：" prop="productFiles"
                                        >
                                            <el-upload
                                                action="fakeaction" ref="productFileRef"
                                                :file-list="addForm.formData.productFiles" list-type="picture-card"
                                                :before-upload="handleBeforeUpload" :limit="uploadMax"
                                                :on-exceed="handleExceed" :auto-upload="false" :multiple="true"
                                                :on-remove="productFileRemove" :on-change="productFileChange"
                                                :on-preview="handlePictureCardPreview"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item
                                            required class="uploader" label="物资小图 （推荐250x200）：" prop="minFile"
                                        >
                                            <el-upload
                                                :class="addForm.minFileLength === 1 ? 'hide_box_min' : ''"
                                                action="fakeaction" ref="minFileRef"
                                                :file-list="addForm.formData.minFile" list-type="picture-card"
                                                :before-upload="handleBeforeUpload" :auto-upload="false"
                                                :on-remove="minFileRemove" :on-change="minFileChange"
                                                :on-preview="handlePictureCardPreview"
                                            >
                                                <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="物资描述：" prop="productDescribe">
                                            <editor v-model="addForm.formData.productDescribe" @blur="onEditorBlur"></editor>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                    </div>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button type="success" @click="submit">保存</el-button>
            <!-- <el-button type="primary" v-if="viewType != 'add' && (addForm.formData.supplierSubmitState == 1 || addForm.formData.supplierSubmitState == 4)" @click="onSubmit">保存并提交</el-button> -->
            <el-button @click="handleClose">返回</el-button>
        </div>
        <!--        表格-->
        <el-dialog
            v-dialogDrag v-loading="brandTableLoading" custom-class="dlg" title="选择品牌"
            :visible.sync="showBrandDialog" width="60%" style="margin-left: 20%;" :close-on-click-modal="false"
        >
            <!--            <div class="box-left">-->
            <!--                <select-material-class @classNodeClick="classNodeClick" ref="materialClassRef" :productType = "0"/>-->
            <!--            </div>-->
            <div class="e-table box-right" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            style="width: 200px" type="text" @blur="getBrandTableData" placeholder="输入搜索关键字"
                            v-model="brand.keywords"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getBrandTableData"/>
                        </el-input>
                    </div>
                </div>
                <el-table
                    ref="tableRef" highlight-current-row border style="width: 100%" :data="brand.tableData"
                    class="table" @row-click="handleCurrentClick" :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="name" label="品牌名" width="150"></el-table-column>
                    <!--                    <el-table-column prop="logo" label="品牌logo" width="90"></el-table-column>-->
                    <el-table-column prop="descript" label="介绍" width=""></el-table-column>
                    <el-table-column prop="gmtCreate" label="创建时间" width="160"></el-table-column>
                    <el-table-column prop="gmtModified" label="更新时间" width="160"></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="brand.tableData != null || brand.tableData.length != 0" :total="brand.paginationInfo.total"
                    :pageSize.sync="brand.paginationInfo.pageSize" :currentPage.sync="brand.paginationInfo.currentPage"
                    @currentChange="getBrandTableData" @sizeChange="getBrandTableData"
                />
                <el-button style="margin-top: 20px" @click="showBrandDialog = false">取消</el-button>
            </span>

        </el-dialog>
        <el-dialog
            v-dialogDrag v-loading="shopDialogLoading" title="选择店铺" :visible.sync="showShopDialog" width="70%"
            style="margin-left: 20%;" :close-on-click-modal="false"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            clearable type="text" @blur="getShopTableList" placeholder="输入搜索关键字"
                            v-model="shop.keywords"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getShopTableList"/>
                        </el-input>
                    </div>
                </div>
                <el-table
                    ref="tableRef" highlight-current-row border style="width: 100%" :data="shop.tableData" class="table"
                    @row-click="shopTableRowClick" :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="shopName" label="店铺名称" width=""></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="shop.tableData != null || shop.tableData.length != 0" :total="shop.paginationInfo2.total"
                    :pageSize.sync="shop.paginationInfo2.pageSize" :currentPage.sync="shop.paginationInfo2.currentPage"
                    @currentChange="getShopTableList" @sizeChange="getShopTableList"
                />
                <el-button style="margin-top: 20px" @click="showShopDialog = false">取消</el-button>
            </span>

        </el-dialog>
        <!--        选择物资库-->
        <el-dialog
            v-dialogDrag v-loading="inventoryTableLoading" title="选择物资库" :visible.sync="showDeviceDialog"
            width="70%" style="margin-left: 20%;" :close-on-click-modal="false"
        >
            <div class="e-table" style="background-color: #fff">
                <div class="top" style="height: 50px; padding-left: 10px">
                    <div class="left">
                        <el-input
                            clearable type="text" @blur="getDeviceInventory" placeholder="输入搜索关键字"
                            v-model="inventory.keyWord"
                        >
                            <img :src="require('@/assets/search.png')" slot="suffix" @click="getDeviceInventory"/>
                        </el-input>
                    </div>
                </div>
                <el-table
                    ref="tableRef" highlight-current-row border style="width: 100%" :data="inventory.tableData"
                    class="table" @row-click="handleCurrentInventoryClick" :max-height="$store.state.tableHeight"
                >
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column prop="billNo" label="编号" width="200"></el-table-column>
                    <el-table-column prop="materialName" label="名称" width="200"></el-table-column>
                    <el-table-column prop="spec" label="规格型号" width=""></el-table-column>
                    <el-table-column prop="className" label="类别名称" width=""></el-table-column>
                    <el-table-column prop="unit" label="计量单位" width=""></el-table-column>
                </el-table>
            </div>
            <span slot="footer">
                <Pagination
                    v-show="inventory.tableData != null || inventory.tableData.length != 0"
                    :total="inventory.paginationInfo.total" :pageSize.sync="inventory.paginationInfo.pageSize"
                    :currentPage.sync="inventory.paginationInfo.currentPage" @currentChange="getDeviceInventory"
                    @sizeChange="getDeviceInventory"
                />
                <el-button style="margin-top: 20px" @click="showDeviceDialog = false">取消</el-button>
            </span>
        </el-dialog>
        <el-dialog :visible.sync="imgPreviewDialog" title="图片预览">
            <img class="center mb20" style="display: block" :src="previewImg" alt="">
        </el-dialog>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>

<script>
import SelectMaterialClass from '@/components/classTree'
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import CategoryCascader from '@/components/category-cascader'
import editor from '@/components/quillEditor'
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
import { mapState } from 'vuex'
import $ from 'jquery'
import { addImgUrl, spliceImgUrl, throttle, toFixed } from '@/utils/common'
import { createFileRecordDelete } from '@/api/shopManage/fileRecordDelete/fileRecordDelete'
import { getBrandPageList } from '@/api/shopManage/brand/brand'
import { uploadFile } from '@/api/platform/common/file'
import {
    createMaterialSupplier,
    onSubmitMaterialSupplier,
    getMaterialInfoSupplier,
    listShopListBySupplierId,
    updateMaterialSupplier,
} from '@/api/shopManage/product/materialManage'
import { updateProductState } from '@/api/platform/product/materialManage'
import { queryPageMaterialDtl } from '@/api/shopManage/product/prodcutInventory'
import { getEnterpriseInfoTaxRate } from '@/api/platform/shop/shopManager'
import { getList } from '@/api/platform/system/systemParam'
export default {
    data () {
        return {
            regionTableIndex: 7,
            regionTableIndex1: 1,
            regionTableIndex2: 1,
            regionTableIndex3: 1,
            regionTableIndex4: 1,
            regionTableIndex5: 1,
            regionTableIndex6: 1,
            addressData: regionData, // 地址数据
            economizeData: [], //只有省数据
            marketData: [], //省市数据
            zoneListLoading: false,
            zoneList: [],
            imgPreviewDialog: false,
            dialogVisible: false,
            previewImg: '',
            mainImg: '',
            smallImg: '',
            dialogImageUrl: '',
            uploadMax: 10,
            init: {
                inventory: {
                    state: 1,
                    productType: 0,
                },
            },
            shopDialogLoading: false,
            // 数据加载
            formLoading: false,
            brandTableLoading: false,
            inventoryTableLoading: false,
            formRules: {
                relevanceName: [
                    { required: true, message: '请选择名称', trigger: 'change' },
                ],
                // brandName: [
                //     { required: true, message: '请选择品牌', trigger: 'blur' },
                // ],
                productName: [
                    { required: true, message: '请输入物资名称', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                classId: [
                    { required: true, message: '请选择分类', trigger: 'blur' },
                    // { validator: this.judgeClassId, message: '请选择低值易耗品下面的分类', trigger: 'blur' },
                ],
                shopId: [
                    { required: true, message: '请选择店铺', trigger: 'change' },
                ],
                productTexture: [
                    { required: true, message: '请输入材质', trigger: 'blur' },
                ],
                annualizedRate: [
                    { required: true, message: '请输入年化率', trigger: 'blur' },
                ],
                purchasePrice: [
                    { required: true, message: '请输入采购进价', trigger: 'blur' },
                ],
                accountPeriod: [
                    { required: true, message: '请选择账期', trigger: 'blur' },
                ],
                taxInPrice: [{ required: true, message: '请输入供货价', trigger: 'change' }],
                payBeforeDelivery: [{ required: true, message: '请输入先款后货', trigger: 'change' }],
                // productMinPrice: [
                //     { required: true, message: '请输入最低价格', trigger: 'blur' },
                //     { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                // ],
                settlePrice: [
                    { required: true, message: '请输入结算价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                skuName: [
                    { required: true, message: '请输入规格', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
                stock: [
                    { required: true, message: '请输入库存', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,3})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                costPrice: [
                    { required: true, message: '请输入供货价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                unit: [
                    { required: true, message: '请选择计量单位', trigger: 'change' },
                ],
                originalPrice: [
                    { required: true, message: '请输入原价', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                sellPrice: [
                    { required: true, message: '请输入销售价格', trigger: 'blur' },
                    { pattern: /^([0-9]+[\d]*(.[0-9]{1,2})?)$/, message: '数据格式错误', trigger: 'blur' },
                ],
                // province: [
                //     { required: true, message: '请选择地址', trigger: 'blur' },
                // ],
                // detailedAddress: [
                //     { required: true, message: '请输入详细地址', trigger: 'blur' },
                //     { min: 1, max: 250, message: '超出范围', trigger: 'blur' }
                // ],
                adminFile: [
                    { required: true, message: '请上传物资主图', trigger: 'change' },
                ],
                productFiles: [
                    { required: true, message: '请上传物资图片', trigger: 'blur' },
                ],
                minFile: [
                    { required: true, message: '请上传物资小图', trigger: 'blur' },
                ],
                productDescribe: [
                    { required: true, message: '请输入描述', trigger: 'blur' },
                ],
                taxRate: [
                    { required: true, message: '请输入税率', trigger: 'blur' },
                ]
            },
            rowData: null, // 跳转过来的数据
            showForm: false,
            // 商品库
            inventory: {
                tableData: [],
                keyWord: null,
                classId: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            showDeviceDialog: false, // 商品库弹窗
            viewType: null,
            showBrandDialog: false, // 品牌弹窗
            showShopDialog: false, // 店铺
            uploadImgSize: 10, // 上传文件大小
            //表单数据
            formData: {},
            currentTab: 1,
            accountPeriodOptions: [{
                value: 1,
                label: '1个月账期'
            }, {
                value: 2,
                label: '2个月账期'
            }, {
                value: 3,
                label: '3个月账期'
            }, {
                value: 4,
                label: '4个月账期'
            }, {
                value: 5,
                label: '5个月账期'
            }, {
                value: 6,
                label: '6个月账期'
            }],
            addForm: {
                formData: {
                    annualizedRate: null,
                    taxRate: null,
                    accountPeriod: [1],
                    regionTableData: [
                        { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 1, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                        { index: 2, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 2, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                        { index: 3, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 3, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                        { index: 4, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 4, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                        { index: 5, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 5, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] },
                        { index: 6, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 6, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] }
                    ],
                    currentRegionTableData: [
                        { index: 1, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: 1, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] }
                    ],
                    zone: '',

                    zoneAddrList: [],
                    zoneId: '',
                    zonePath: '',
                    productType: 0,
                    relevanceName: null,
                    productName: null,
                    productKeyword: null,
                    classId: null,
                    classPath: [],
                    productMinPrice: null,
                    brandId: null,
                    brandName: null,
                    shopSort: null,
                    skuName: null,
                    settlePrice: null,
                    costPrice: null,
                    stock: 1,
                    unit: null,
                    originalPrice: null,
                    sellPrice: null,
                    province: null,
                    city: null,
                    county: null,
                    detailedAddress: null,
                    productFiles: [],
                    minFile: [],
                    adminFile: [],
                    zonePriceList: [],
                    productDescribe: null,
                    shopId: null,
                },
                adminFileLength: 0,
                minFileLength: 0,
                // 地址
                addressData: regionData, // 地址数据
                selectAddressOptions: [], // 地址选择
                // 计量单位
                numUnitOptions: [],
            },
            // 品牌数据
            brand: {
                classId: null,
                keywords: null,
                tableData: [],
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            shop: {
                keywords: null,
                tableData: [],
                paginationInfo2: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
        }
    },
    components: {
        // eslint-disable-next-line vue/no-unused-components
        SelectMaterialClass,
        Pagination,
        CategoryCascader,
        editor,
    },
    created () {
        this.addForm.numUnitOptions = this.materialUnit
        this.rowData = this.$route.params.row
        if (this.rowData.viewType == 'add') {
            this.getEnterpriseInfoTaxRateM()
            this.viewType = 'add'
            if (this.rowData.classPath != null) {
                this.addForm.formData.classPath = this.rowData.classPath
                this.addForm.formData.classId = this.rowData.classPath[this.rowData.classPath.length - 1]
            }
            this.showForm = true
            this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
            getList({ keywords: '账期年化利率' }).then(res => {
                this.addForm.formData.annualizedRate = Number(res.list[0].keyValue)
            })
        } else {
            this.getMaterialInfo()
        }
        this.getEconomizeAndMarketList()
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo']
        this.tabArr = arr
        let $idsTop = []
        const onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        this.winEvent.onScroll = onScroll
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                const itemTop = document.getElementById(item).offsetTop
                return itemTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['materialUnit']),
        ...mapState({
            options: state => state.contract.ctClassify,
            userInfo: state => state.userInfo,
        }),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        },
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        handleAddressChange (index) {
            let regionOption = this.addForm.formData.regionTableData.filter(item => item.index === index)[0]
            let addArr = regionOption.selectAddressOptions
            if (addArr != null && addArr.length > 0) {
                let addrStr = []
                addArr.forEach(e=>{
                    if (e.length == 1) {
                        addrStr.push(CodeToText[e[0]])
                    }else if (e.length == 2) {
                        addrStr.push(CodeToText[e[0]] + CodeToText[e[1]])
                    }
                })
                regionOption.detailAddress = addrStr
                //this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
            }
        },
        handleAddressChange1 (params) {
            if (params != null && params.length > 0) {
                let addrStr = []
                params.forEach(e=>{
                    addrStr.push(CodeToText[e])
                })
                this.addForm.formData.regionTableData.filter(item => item.index === this.currentTab)[0].detailAddress = addrStr
                //this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
            }
        },
        //获取当前企业的企业税率
        getEnterpriseInfoTaxRateM () {
            getEnterpriseInfoTaxRate().then(res=>{
                this.addForm.formData.taxRate = res
            })
        },
        getEconomizeAndMarketList () {
            let economizeList = []
            let marketList = []
            this.addressData.forEach((e, i)=>{
                economizeList.push( { label: e.label, value: e.value } )
                marketList.push( { label: e.label, value: e.value, children: [] } )
                e.children.forEach(s=>{
                    marketList[i].children.push({ label: s.label, value: s.value })
                })
            })
            this.economizeData = economizeList
            this.marketData = marketList
        },
        getCurrentTab (val) {
            this.currentTab = val
            this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
        },
        addRegion () {
            if (this.currentTab === 1) {
                this.addForm.formData.regionTableData.push({ index: this.regionTableIndex++, regionName: '区域' + this.regionTableIndex1++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 2) {
                this.addForm.formData.regionTableData.push({ index: this.regionTableIndex++, regionName: '区域' + this.regionTableIndex2++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 3) {
                this.addForm.formData.regionTableData.push({ index: this.regionTableIndex++, regionName: '区域' + this.regionTableIndex3++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 4) {
                this.addForm.formData.regionTableData.push({ index: this.regionTableIndex++, regionName: '区域' + this.regionTableIndex4++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 5) {
                this.addForm.formData.regionTableData.push({ index: this.regionTableIndex++, regionName: '区域' + this.regionTableIndex5++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            } else if (this.currentTab === 6) {
                this.addForm.formData.regionTableData.push({ index: this.regionTableIndex++, regionName: '区域' + this.regionTableIndex6++, selectAddressOptions: [], accountPeriod: this.currentTab, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
            }
            this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
        },
        removeOneParamsData (row) {
            if (row.index < 7) {
                let region = this.addForm.formData.regionTableData.filter(item => item.index === row.index)[0]
                region.taxInPrice = ''
                region.price = ''
                region.payBeforeDelivery = ''
                region.selectAddressOptionsAll = []
                region.detailAddress = []
            } else {
                this.addForm.formData.regionTableData = this.addForm.formData.regionTableData.filter(obj => obj.index !== row.index)
            }
            this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
        },
        payBeforeDeliveryChange (index) {
            let regionOption = this.addForm.formData.currentRegionTableData.filter(item => item.index === index)[0]
            if (this.addForm.formData.annualizedRate === null || this.addForm.formData.annualizedRate === '') {
                regionOption.payBeforeDelivery = ''
                return this.$message.error('年化率不能为空')
            }
            let taxInPrice = regionOption.payBeforeDelivery * (1 + (this.addForm.formData.annualizedRate / 1200 * regionOption.accountPeriod))
            regionOption.taxInPrice = this.fixed2(taxInPrice)
            if (this.addForm.formData.taxRate === null || this.addForm.formData.taxRate === '') {
                regionOption.payBeforeDelivery = ''
                regionOption.taxInPrice = ''
                return this.$message.error('税率不能为空')
            }
            regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
        },
        taxInPriceChange (index) {
            let regionOption = this.addForm.formData.currentRegionTableData.filter(item => item.index === index)[0]
            if (this.addForm.formData.taxRate == null || this.addForm.formData.taxRate == '') {
                regionOption.taxInPrice = ''
                return this.$message.error('税率不能为空')
            }
            regionOption.taxInPrice = this.fixed2(regionOption.taxInPrice)
            regionOption.price = this.fixed2(regionOption.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
        },
        validateAdminFile (rule, value, callback) {
            if (this.addForm.adminFileLength === 0) {
                callback(new Error('请上传物资主图'))
            } else {
                callback()
            }
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },

        // judgeClassId (rule, value, callback) {
        //     console.log(value)
        //     console.log(callback)
        //     // callback()
        // },
        resetRelevanceAndBrand () {
            let arr = ['relevanceName', 'brandName', 'brandId', 'relevanceId']
            arr.forEach(item => this.addForm.formData[item] = '')
            this.$refs.formEdit.validateField()
        },
        // 物资库点击
        handleCurrentInventoryClick (row) {
            this.addForm.formData.relevanceName = row.materialName
            this.addForm.formData.relevanceId = row.billId
            this.addForm.formData.relevanceNo = row.billNo
            this.addForm.formData.classId = row.classId
            this.addForm.formData.classPath = row.classIdPath.split('/')
            this.showDeviceDialog = false
            this.$refs.formEdit.validateField('classId')
        },
        // 获取物资库
        getDeviceInventory () {
            let params = {
                isActive: 1,
                pageIndex: this.inventory.paginationInfo.currentPage,
                pageSize: this.inventory.paginationInfo.pageSize,
            }
            // if (this.inventory.classId != null) {
            //     params.classId = this.inventory.classId
            // }
            params.classId = 'a927249b2810-a00f-1d43-ef73-78cbd5be'//低值易耗品
            if (this.inventory.keyWord != null) {
                params.keyWord = this.inventory.keyWord
            }
            this.inventoryTableLoading = true
            queryPageMaterialDtl(params).then(res => {
                this.inventory.tableData = res.list
                this.inventory.paginationInfo.total = res.totalCount
                this.inventory.paginationInfo.pageSize = res.pageSize
                this.inventory.paginationInfo.currentPage = res.currPage
                this.inventoryTableLoading = false
            }).catch(() => {
                this.inventoryTableLoading = false
            })
        },
        handleExceed () {
            this.$message.warning('请最多上传 ' + this.uploadMax + ' 个文件。')
        },
        // 修改状态
        updateStateBatch (state, title) {
            let params = {
                productIds: [this.addForm.formData.productId],
                state: state
            }
            this.clientPop('info', '您确定要' + title + '这个物资吗！', async () => {
                updateProductState(params).then(res => {
                    this.handleClose()
                    this.message(res)
                })
            })
        },
        // 获取物资详情
        getMaterialInfo () {
            let params = {
                productId: this.rowData.productId,
                productType: 0
            }
            this.formLoading = true
            getMaterialInfoSupplier(params).then(res => {
                this.addressFormatShow(res)
                addImgUrl(res, this.imgUrlPrefixAdd)
                this.addForm.minFileLength = res.minFile.length
                this.addForm.adminFileLength = res.adminFile.length
                res['currentRegionTableData'] = []
                res['regionTableData'] = []
                this.addForm.formData = res
                //this.addForm.formData.regionTableData = res.regionPrice
                this.inventory.classId = this.addForm.formData.classId
                this.addForm.formData.accountPeriod = res.accountPeriod.split(',').map(Number)
                this.currentTab = this.addForm.formData.accountPeriod[0]
                let regionList = []
                for (let i = 1;i < 7;i++) {
                    let regionOption = res.regionPrice.filter(item1 => item1.accountPeriod === i)
                    if (regionOption.length > 0) {
                        regionOption[0]['index'] = i
                        regionList.push(regionOption[0])
                        regionOption.splice(0, 1)
                        for (let item of regionOption) {
                            item.index = this.regionTableIndex++
                            regionList.push(item)
                        }
                        if (regionOption.length > 0) {
                            let indexNumber = Number(regionOption[regionOption.length - 1].regionName.replace('区域', '')) + 1
                            switch (i) {
                            case 1:
                                this.regionTableIndex1 = indexNumber
                                break
                            case 2:
                                this.regionTableIndex2 = indexNumber
                                break
                            case 3:
                                this.regionTableIndex3 = indexNumber
                                break
                            case 4:
                                this.regionTableIndex4 = indexNumber
                                break
                            case 5:
                                this.regionTableIndex5 = indexNumber
                                break
                            case 6:
                                this.regionTableIndex6 = indexNumber
                                break
                            }
                        }
                    } else {
                        regionList.push({ index: i, regionName: '全区域', selectAddressOptionsAll: [], accountPeriod: i, payBeforeDelivery: '', taxInPrice: '', price: '', detailAddress: [] })
                    }
                }
                this.addForm.formData.regionTableData = regionList
                this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
                this.showForm = true
                this.formLoading = false
            }).catch(() => {
                this.formLoading = false
            })
        },
        arChange () {
            if (this.addForm.formData.annualizedRate == null || this.addForm.formData.annualizedRate == '') {
                return this.$message.error('年化率不能为空')
            }
            if (!(0 <= this.addForm.formData.annualizedRate && this.addForm.formData.annualizedRate <= 100)) {
                this.addForm.formData.annualizedRate = ''
                return this.$message.error('年化率不能小于0或大于100')
            }
            if (this.addForm.formData.regionTableData.length > 0) {
                this.addForm.formData.regionTableData.forEach(e=>{
                    if (e.payBeforeDelivery.length > 0) {
                        let taxInPrice = e.payBeforeDelivery * (1 + (this.addForm.formData.annualizedRate / 1200 * e.accountPeriod))
                        e.taxInPrice = this.fixed2(taxInPrice)
                        e.price = this.fixed2(e.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
                    }
                })
            }
        },
        writeTaxRate () {
            if (this.addForm.formData.taxRate == null || this.addForm.formData.taxRate == '') {
                return this.$message.error('税率不能为空')
            }
            if (!(0 <= this.addForm.formData.taxRate && this.addForm.formData.taxRate <= 100)) {
                this.addForm.formData.taxRate = ''
                return this.$message.error('税率不能小于0或大于100')
            }
            this.addForm.formData.taxRate = this.fixed2(this.addForm.formData.taxRate)//税率保留2位小数
            if (this.addForm.formData.regionTableData.length > 0) {
                this.addForm.formData.regionTableData.forEach(r => {
                    if (r.taxInPrice !== null && r.taxInPrice !== '') {
                        r.price = this.fixed2(r.taxInPrice / (1 + (this.addForm.formData.taxRate / 100)))
                    }
                })
                //this.addForm.formData.currentRegionTableData = this.addForm.formData.regionTableData.filter(item => item.accountPeriod === this.currentTab)
            }
        },
        // 计量单位选择
        numUnitChange (value) {
            this.addForm.formData.unit = value
        },
        // 地址回显
        addressFormatShow (row) {
            if (row.province == null) {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.addForm.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },

        onSubmit () {
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.addForm.formData.state = 2
                    spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                    this.formLoading = true
                    if (this.viewType === 'add') {
                        onSubmitMaterialSupplier(this.addForm.formData).then(res => {
                            if (res.code != null && res.code != 200) {
                                addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                                this.formLoading = false
                                return
                            }
                            let classInfo = {
                                classPath: this.addForm.formData.classPath,
                                classId: this.addForm.formData.classId
                            }
                            // 重置
                            this.resetFormData()
                            // 恢复分类
                            this.addForm.formData.classPath = classInfo.classPath
                            this.addForm.formData.classId = classInfo.classId
                            this.$refs.adminFileRef.clearFiles()
                            this.$refs.productFileRef.clearFiles()
                            this.$refs.minFileRef.clearFiles()
                            this.addForm.minFileLength = 0
                            this.addForm.adminFileLength = 0
                            this.formLoading = false
                            this.message(res)
                            this.handleClose()
                        }).catch(() => {
                            this.formLoading = false
                        }).finally(() => {
                            this.formLoading = false
                        })
                    }else {
                        this.formLoading = true
                        updateMaterialSupplier(this.addForm.formData).then(res => {
                            this.getMaterialInfo()
                            this.formLoading = false
                            this.message(res)
                            this.$router.go(-1)
                        }).catch(() => {
                            this.getMaterialInfo()
                            this.formLoading = false
                        }).finally(() => {
                            this.formLoading = false
                        })
                    }
                }/*else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }*/
            })
        },
        onEditorBlur () {
            this.$refs.formEdit.validateField('productDescribe')
        },

        // 提交
        submit () {
            this.addForm.formData.regionPrice = []
            for (let item of this.addForm.formData.accountPeriod) {
                let regionList = this.addForm.formData.regionTableData.filter(item1 => item1.accountPeriod === item)
                this.addForm.formData.regionPrice = this.addForm.formData.regionPrice.concat(regionList)
            }
            console.log(this.addForm.formData)
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.addForm.formData.accountPeriod = this.addForm.formData.accountPeriod.toString()
                    spliceImgUrl(this.addForm.formData, this.imgUrlPrefixDelete)
                    if (this.viewType === 'add') {
                        this.formLoading = true
                        createMaterialSupplier(this.addForm.formData).then(res => {
                            if (res.code != null && res.code != 200) {
                                addImgUrl(this.addForm.formData, this.imgUrlPrefixAdd)
                                this.formLoading = false
                                return
                            }
                            let classInfo = {
                                classPath: this.addForm.formData.classPath,
                                classId: this.addForm.formData.classId
                            }
                            // 重置
                            this.resetFormData()
                            // 恢复分类
                            this.addForm.formData.classPath = classInfo.classPath
                            this.addForm.formData.classId = classInfo.classId
                            this.$refs.adminFileRef.clearFiles()
                            this.$refs.productFileRef.clearFiles()
                            this.$refs.minFileRef.clearFiles()
                            this.addForm.minFileLength = 0
                            this.addForm.adminFileLength = 0
                            this.formLoading = false
                            this.message(res)
                            this.handleClose()
                        }).catch(() => {
                            this.formLoading = false
                            this.addForm.formData.accountPeriod = this.addForm.formData.accountPeriod.split(',')
                        }).finally(() => {
                            this.formLoading = false
                        })
                    } else {
                        this.formLoading = true
                        updateMaterialSupplier(this.addForm.formData).then(res => {
                            this.getMaterialInfo()
                            this.formLoading = false
                            this.message(res)
                            this.handleClose()
                        }).catch(() => {
                            this.getMaterialInfo()
                            this.formLoading = false
                            this.addForm.formData.accountPeriod = this.addForm.formData.accountPeriod.split(',')
                        }).finally(() => {
                            this.formLoading = false
                        })
                    }
                }/* else {
                    this.$message({
                        message: '请检查非空输入框',
                        type: 'error'
                    })
                }*/
            })
        },
        // 地区
        // handleAddressChange () {
        //     let addArr = this.addForm.selectAddressOptions
        //     let province = CodeToText[addArr[0]]
        //     let city = CodeToText[addArr[1]]
        //     let county = CodeToText[addArr[2]]
        //     this.addForm.formData.province = province
        //     this.addForm.formData.city = city
        //     this.addForm.formData.county = county
        //     this.addForm.formData.detailedAddress = province + city + county
        // },
        // 小图上传
        minFileChange (file, fileList) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            this.uploadMinFile(file, fileList)
        },
        // 物资主图上传
        adminFileChange (file, fileList) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            this.uploadAdminFile(file, fileList)
        },
        // 物资图片上传
        productFileChange (file, fileList) {
            if (!(file.size / 1024 / 1024 < this.uploadImgSize)) {
                fileList.pop()
                return this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            fileList.pop()
            this.uploadProductFile(file, fileList)
        },
        // 品牌单选
        handleCurrentClick (row) {
            this.addForm.formData.brandId = row.brandId
            this.addForm.formData.brandName = row.name
            this.showBrandDialog = false
        },
        // 店铺点击
        shopTableRowClick (row) {
            this.addForm.formData.shopId = row.shopId
            this.addForm.formData.shopName = row.shopName
            this.showShopDialog = false
        },
        // 分类点击
        classNodeClick (data) {
            this.brand.classId = data.classId
            this.getBrandTableData()
        },
        // 获取品牌表格
        getBrandTableData () {
            let params = {
                page: this.brand.paginationInfo.currentPage,
                limit: this.brand.paginationInfo.pageSize,
            }
            if (this.brand.keywords != null) {
                params.name = this.brand.keywords
            }
            if (this.brand.classId != null) {
                params.classId = this.brand.classId
            }
            this.brandTableLoading = true
            getBrandPageList(params).then(res => {
                this.brand.tableData = res.list
                this.brand.paginationInfo.total = res.totalCount
                this.brand.paginationInfo.pageSize = res.pageSize
                this.brand.paginationInfo.currentPage = res.currPage
                this.brandTableLoading = false
            }).catch(() => {
                this.brandTableLoading = false
            })
        },
        // 获取店铺表格
        getShopTableList () {
            let params = {
                page: this.shop.paginationInfo2.currentPage,
                limit: this.shop.paginationInfo2.pageSize,
            }
            if (this.shop.keywords != null) {
                params.shopName = this.shop.keywords
            }
            this.shopDialogLoading = true
            listShopListBySupplierId(params).then(res => {
                this.shop.tableData = res.list
                this.shop.paginationInfo2.total = res.totalCount
                this.shop.paginationInfo2.pageSize = res.pageSize
                this.shop.paginationInfo2.currentPage = res.currPage
                this.shopDialogLoading = false
            }).catch(() => {
                this.shopDialogLoading = false
            })
        },
        // 选择物资名称
        importDeviceSelect () {
            /*if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            } else {
                this.inventory.classId = this.addForm.formData.classId
            }*/
            this.showDeviceDialog = true
            this.getDeviceInventory()
        },
        shopDialog () {
            this.showShopDialog = true
            this.getShopTableList()
        },
        brandDialog () {
            if (this.addForm.formData.classId == null || this.addForm.formData.classId == '') {
                return this.$message.error('请先选择分类')
            } else {
                this.brand.classId = this.addForm.formData.classId
            }
            this.showBrandDialog = true
            this.getBrandTableData()
        },
        uploadFileInfo (params) {
            const form = new FormData()
            form.append('files', params.raw)
            form.append('bucketName', 'mall')
            form.append('directory', 'material') // 商城类型
            form.append('isChangeObjectName', true)
            form.append('isTemplate', false)
            form.append('orgCode', 'SRBC') // 登录获取
            form.append('relationId', '990116') // 未知
            return form
        },
        // 上传主图
        uploadAdminFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.adminFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 1
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 0
                this.addForm.formData.adminFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.formLoading = false
                this.$refs.formEdit.validateField('adminFile')
            }).catch(() => {
                this.addForm.adminFileLength = 0
                this.formLoading = false
            })
        },
        // 上传物资图片
        uploadProductFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.formLoading = true
            uploadFile(form).then(res => {
                let productFile = {}
                productFile.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                productFile.name = res[0].objectName
                productFile.isMain = 0
                productFile.relevanceType = 1
                productFile.fileType = 1
                productFile.fileFarId = res[0].recordId
                productFile.imgType = 0
                this.addForm.formData.productFiles.push(productFile)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.formLoading = false
                this.$refs.formEdit.validateField('productFiles')
            }).catch(() => {
                this.formLoading = false
            })
        },
        // 上传小图
        uploadMinFile (params, fileList) {
            let form = this.uploadFileInfo(params)
            this.addForm.minFileLength = 1
            this.formLoading = true
            uploadFile(form).then(res => {
                let file = {}
                file.url = res[0].objectPath.replace(this.imgUrlPrefixDelete, this.imgUrlPrefixAdd)
                file.name = res[0].objectName
                file.isMain = 0
                file.relevanceType = 1
                file.fileType = 1
                file.fileFarId = res[0].recordId
                file.imgType = 1
                this.addForm.formData.minFile.push(file)
                fileList.push(params)
                this.$message({
                    message: '上传成功',
                    type: 'success'
                })
                this.formLoading = false
                this.$refs.formEdit.validateField('minFile')
            }).catch(() => {
                this.addForm.minFileLength = 0
                this.formLoading = false
            })
        },
        // 判断上传的图片大小
        handleBeforeUpload (file) {
            const sizeOk = file.size / 1024 / 1024 < this.uploadImgSize
            if (!sizeOk) {
                this.$message.error(`上传的图片大小不能超过 ${this.uploadImgSize}MB!`)
            }
            return sizeOk
        },
        handlePictureCardPreview (file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        },
        // 主图删除
        adminFileRemove (file, fileList) {
            this.addForm.adminFileLength = fileList.length
            let recordId = this.addForm.formData.adminFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.adminFile = []
                this.$refs.formEdit.validateField('adminFile')
            })
        },
        // 物资图片删除
        productFileRemove (file) {
            let files = this.addForm.formData.productFiles
            let recordId = null
            let newFiles = files.filter(t => {
                if (file.name == t.name) {
                    recordId = t.fileFarId
                    return false
                } else {
                    return true
                }
            })
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.productFiles = newFiles
                this.$refs.formEdit.validateField('productFiles')
            })

        },
        // 小图删除
        minFileRemove (file, fileList) {
            this.addForm.minFileLength = fileList.length
            let recordId = this.addForm.formData.minFile[0].fileFarId + ''
            createFileRecordDelete({ recordId: recordId }).then(res => {
                this.message(res)
                this.addForm.formData.minFile = []
                this.$refs.formEdit.validateField('minFile')
            })
        },
        //取消
        handleClose () {
            this.$router.go(-1)
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    const lastConHeight =
                        document.getElementById('terminationLog').offsetHeight
                    this.lastConHeight = lastConHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 重置数据
        resetFormData () {
            this.addForm.selectAddressOptions = []
            this.addForm.formData = {
                productType: 0,
                relevanceName: null,
                productName: null,
                productKeyword: null,
                classId: null,
                classPath: [],
                productMinPrice: null,
                brandId: null,
                brandName: null,
                shopSort: null,
                skuName: null,
                settlePrice: null,
                costPrice: null,
                stock: 1,
                unit: null,
                originalPrice: null,
                sellPrice: null,
                province: null,
                city: null,
                county: null,
                detailedAddress: null,
                productFiles: [],
                adminFile: [],
                minFile: [],
                productDescribe: null,
                shopId: null,
            }
        },
        // 消息提示
        message (res) {
            this.$message({
                message: res.message,
                type: res.code == 200 ? 'success' : 'error'
            })
        },
    }
}
</script>

<style lang='scss' scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}

.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {
        width: 0;
    }
}

// 上传成功后隐藏上传按钮
/deep/ .hide_box_admin .el-upload--picture-card {
    display: none;
}

/deep/ .hide_box_min .el-upload--picture-card {
    display: none;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 600px;
        margin-top: 0px;
    }
}
.tabContent {
  margin-left: 9%;
  margin-bottom: 1%;
  border-bottom: 1px solid #cdcdcd;
}
.accountPeriodTabs {
  font-size: 19px;
  display: inline-block;
  margin-right: 2%;
  .tabTitle {
    border-bottom: 5px solid #2e65d9;
    padding: 6px 0;
    color: #2e65d9;
    cursor: pointer;
  }
  .tabTitleNoSelect {
    padding: 6px 0;
    cursor: pointer;
  }
}
</style>
