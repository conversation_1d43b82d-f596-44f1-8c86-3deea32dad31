export default [
    {
        path: '/mFront',
        name: 'mFront',
        component: () => import('@/pages/frontStage/index'),
        redirect: '/index',
        children: [
            {
                path: '/index',
                component: () => import('@/pages/frontStage/homePage'),
                meta: {
                    title: '物资采购平台-首页'
                },
                children: []
            },
            {
                path: '/login/:user?',
                // 开启props 会把URL中的参数传递给组件
                // 在组件中通过props来接收
                props: true,
                name: 'login',
                component: () => import(/* webpackChunkName: "index" */ '@/pages/frontStage/login'),
                meta: {
                    title: '登录'
                }
            },
            {
                path: '/mFront/webInfo',
                component: () => import('@/pages/frontStage/webInfo/index'),
                children: [
                    {
                        path: '/mFront/simpleDetail',
                        component: () => import('@/pages/frontStage/webInfo/simpleDetail'),
                    },
                    {
                        path: '/mFront/webInfoList',
                        component: () => import('@/pages/frontStage/webInfo/list'),
                    },
                    {
                        path: '/mFront/webInfoListDetail',
                        component: () => import('@/pages/frontStage/webInfo/listDetail'),
                    },
                ],
            },
            {
                path: '/mFront/becomeSeller',
                name: 'becomeSeller',
                component: () => import('@/pages/frontStage/userCenter/becomeSeller/index'),
                meta: {
                    title: '我要开店'
                },
            },
            {
                path: '/mFront/openShop',
                name: 'ApplyToOpenShop',
                component: () => import('@/pages/frontStage/userCenter/openShop/ApplyToOpenShop'),
                meta: {
                    title: '申请开店'
                },
            },
            {
                path: '/mFront/contractSigning',
                name: 'ContractSigningAndPayment',
                component: () => import('@/pages/frontStage/userCenter/contractSigning/ContractSigningAndPayment'),
                meta: {
                    title: '合同签约及缴费'
                },
            },
            {
                path: '/mFront/PlatformReview',
                name: 'PlatformReview',
                component: () => import('@/pages/frontStage/register/PlatformReview'),
                meta: {
                    title: '平台复审'
                },
            },
            {
                path: '/mFront/success',
                name: 'success',
                component: () => import('@/pages/frontStage/register/success'),
                meta: {
                    title: '完成'
                },
            },
            {
                path: '/mFront/register',
                name: 'register',
                component: () => import('@/pages/frontStage/register/register'),
                meta: {
                    title: '用户注册'
                }
            },
            {
                path: '/mFront/materialControl',
                name: 'materialControl',
                component: () => import('@/pages/frontStage/userCenter/materialControl/index'),
                meta: {
                    title: '物资采购'
                }
            },
            {
                path: '/mFront/shopIndex',
                name: 'shopIndex',
                component: () => import('@/pages/frontStage/mall/shop/index'),
                meta: {
                    title: '店铺首页'
                }
            },
            {
                path: '/user/groupIndex',
                name: 'groupIndex',
                component: () => import('@/pages/frontStage/userCenter/group/index'),
                meta: {
                    title: '我的主页'
                }
            },
            {
                path: '/mFront/biddingIndex',
                name: 'biddingIndex',
                component: () => import('@/pages/frontStage/bidding/index'),
                meta: {
                    title: '招标大厅'
                }
            },
            {
                path: '/mFront/biddingNotice',
                name: 'biddingIndex',
                component: () => import('@/pages/frontStage/bidding/notice'),
                meta: {
                    title: '中标公告'
                }
            },
            {
                path: '/mFront/biddingDetail',
                name: 'biddingDetail',
                component: () => import('@/pages/frontStage/bidding/detail'),
                meta: {
                    title: '公开招标详情'
                }
            },
            {
                path: '/mFront/inviteDetail',
                name: 'inviteDetail',
                component: () => import('@/pages/frontStage/bidding/inviteDetail'),
                meta: {
                    title: '邀请招标详情'
                }
            },
            {
                path: '/mFront/enquiryDetail',
                name: 'enquiryDetail',
                component: () => import('@/pages/frontStage/bidding/enquiryDetail'),
                meta: {
                    title: '招标-询价详情'
                }
            },
            {
                path: '/mFront/competedetail',
                name: 'competedetail',
                component: () => import('@/pages/frontStage/bidding/competeDetail'),
                meta: {
                    title: '竞争性谈判招标详情'
                }
            },
            {
                path: '/mFront/oneTender',
                name: 'oneTender',
                component: () => import('@/pages/frontStage/bidding/oneTender'),
                meta: {
                    title: '单一性来源招标详情'
                }
            },
            {
                path: '/mFront/mallIndex',
                name: 'mallIndex',
                component: () => import('@/pages/frontStage/mall/index'),
                children: [
                ],
                meta: {
                    title: '首页'
                }
            },
            {
                path: '/mFront/massPriceList',
                name: '',
                component: () => import('@/pages/frontStage/mall/massPurchase/topList'),
                meta: {
                    title: '大宗价格公示'
                }
            },
            {
                path: '/mFront/biddingDisplayDetail',
                name: 'mallIndex',
                component: () => import('@/pages/frontStage/mall/biddingDisplay/detail'),
                children: [
                ],
                meta: {
                    title: '首页'
                }
            },
            {
                path: '/mFront/productList',
                name: 'productList',
                component: () => import('@/pages/frontStage/mall/productList/index'),
                meta: {
                    title: '商品列表'
                }
            },
            {
                path: '/mFront/productComparison',
                name: 'productComparison',
                component: () => import('@/pages/frontStage/mall/productComparison/index'),
                meta: {
                    title: '商品对比',
                }
            },
            {
                path: '/mFront/shopProductList',
                name: 'shopProductList',
                component: () => import('@/pages/frontStage/mall/shop/products.vue'),
                meta: {
                    title: '商品列表'
                }
            },
            {
                path: '/mFront/shopList',
                name: 'shopList',
                component: () => import('@/pages/frontStage/mall/shopList/index'),
                meta: {
                    title: '店铺列表'
                }
            },
            {
                path: '/mFront/supplierIndex',
                name: 'supplierIndex',
                component: () => import('@/pages/frontStage/mall/supplier/index'),
                meta: {
                    title: '供应商'
                }
            },
            {
                path: '/mFront/brands',
                name: 'brands',
                component: () => import('@/pages/frontStage/mall/brands/allBrands'),
                meta: {
                    title: '所有品牌'
                },
            },
            {
                path: '/mFront/productDetail',
                component: () => import('@/pages/frontStage/mall/productDetail/index'),
                meta: {
                    title: '商品详情'
                },
            },
            {
                path: '/user/orderPlanDetail',
                name: 'orderPlanDetail',
                component: () =>
                    import('@/pages/frontStage/userCenter/order/detail.vue'),
                meta: {
                    title: '订单详情',
                },
            },
            {
                path: '/user/orderPlanlgDetail',
                name: 'orderPlanlgDetail',
                component: () =>
                    import('@/pages/frontStage/userCenter/order/lgdetail.vue'),
                meta: {
                    title: '订单详情',
                },
            },
            {
                path: '/user/monthPlanDtl',
                name: 'monthPlanDtl',
                component: () =>
                    import('@/pages/frontStage/userCenter/plan/month/detail.vue'),
                meta: {
                    title: '月供计划明细',
                },
            },
            {
                path: '/user/updateChangeDetail',
                name: 'updateChangeDetail',
                component: () =>
                    import('@/pages/frontStage/userCenter/plan/month/updateChangeDetail.vue'),
                meta: {
                    title: '月供计划变更明细',
                },
            },
            {
                path: '/user',
                name: 'user',
                component: () => import('@/pages/frontStage/userCenter/index'),
                redirect: '/user/userCenter',
                children: [
                    {
                        path: '/user/feedback',
                        name: 'feedback',
                        component: () =>
                            import('@/pages/frontStage/userCenter/feedback/index.vue'),
                        meta: {
                            title: '反馈中心',
                        },
                    },
                    {
                        path: '/user/chart',
                        name: 'chart',
                        component: () =>
                            import('@/pages/frontStage/userCenter/chart/index.vue'),
                        meta: {
                            title: '统计分析',
                        },
                    },
                    {
                        path: '/user/submitOrderByPlanFull',
                        name: 'submitOrderByPlanFull',
                        component: () =>
                            import('@/pages/frontStage/userCenter/plan/submitCartOrder.vue'),
                        meta: {
                            title: '提交订单',
                        },
                    },
                    {
                        path: '/user/submitOrderByPlan',
                        name: 'submitOrderByPlan',
                        component: () =>
                            import('@/pages/frontStage/userCenter/plan/submitOrderByPlan.vue'),
                        meta: {
                            title: '提交计划订单',
                        },
                    },
                    {
                        path: '/user/plan/:type',
                        name: 'plan',
                        component: () =>
                            import('@/pages/frontStage/userCenter/plan/index.vue'),
                        meta: {
                            title: route => {
                                const type = route.params.type
                                switch (type) {
                                case '0':
                                    return '零星采购计划列表'
                                case '1':
                                    return '大宗临购计划列表'
                                case '2':
                                    return '周转材料计划列表'
                                default:
                                    return ''
                                }
                            },
                        },
                    },
                    {
                        path: '/user/synthesizeTemporary',
                        name: 'synthesizeTemporary',
                        component: () =>
                            import('@/pages/frontStage/userCenter/synthesizeTemporary/index.vue'),
                        meta: {
                            title: '大宗临购清单',
                        },
                    },
                    {
                        path: '/user/synthesizeTemporaryDtl',
                        name: 'synthesizeTemporaryDtl',
                        component: () =>
                            import('@/pages/frontStage/userCenter/synthesizeTemporary/detail.vue'),
                        meta: {
                            title: '大宗临购清单详情',
                        },
                    },
                    {
                        path: '/user/turnoverMaterials',
                        name: 'turnoverMaterials',
                        component: () =>
                            import('@/pages/frontStage/userCenter/turnoverMaterials/index.vue'),
                        meta: {
                            title: '周转材料清单',
                        },
                    },
                    {
                        path: '/user/turnoverMaterialsDtl',
                        name: 'turnoverMaterialsDtl',
                        component: () =>
                            import('@/pages/frontStage/userCenter/turnoverMaterials/detail.vue'),
                        meta: {
                            title: '周转材料清单详情',
                        },
                    },
                    {
                        path: '/user/monthPlan',
                        name: 'monthPlan',
                        component: () =>
                            import('@/pages/frontStage/userCenter/plan/month/index.vue'),
                        meta: {
                            keepAlive: true,
                            title: '月供计划',
                        },
                    },
                    {
                        path: '/user/planDetail',
                        name: 'planDetail',
                        component: () =>
                            import('@/pages/frontStage/userCenter/plan/detail.vue'),
                        meta: {
                            title: '计划详情',
                        },
                    },
                    {
                        path: '/user/statusFail',
                        name: 'statusFail',
                        component: () => import('@/pages/frontStage/userCenter/statusFail'),
                        meta: {
                            title: '审核失败',
                        },
                    },
                    {
                        path: '/user/userCenter',
                        name: 'userCenter',
                        component: () => import('@/pages/frontStage/userCenter/user/user'),
                        meta: {
                            title: '个人中心'
                        },
                    },
                    {
                        path: '/user/indexBidding',
                        name: 'userCenter',
                        component: () => import('@/pages/frontStage/userCenter/bidding/indexBidding'),
                        meta: {
                            title: '竞价采购'
                        },
                    },
                    {
                        path: '/user/publishBidding',
                        name: 'userCenter',
                        component: () => import('@/pages/frontStage/userCenter/bidding/publishBidding'),
                        meta: {
                            title: '竞价采购'
                        },
                    },
                    {
                        path: '/user/detailBidding',
                        name: 'userCenter',
                        component: () => import('@/pages/frontStage/userCenter/bidding/detailBidding'),
                        meta: {
                            title: '竞价采购'
                        },
                    },
                    {
                        path: '/user/refund',
                        name: 'refund',
                        component: () =>
                            import('@/pages/frontStage/userCenter/refund/index'),
                        meta: {
                            title: '我的退货',
                        },
                    },

                    {
                        path: '/user/refund/detail',
                        name: 'refundDetail',
                        component: () =>
                            import('@/pages/frontStage/userCenter/refund/detail.vue'),
                        meta: {
                            title: '退货详情',
                        },
                    },
                    {
                        path: '/user/refund/invoice',
                        name: 'refundApply',
                        component: () =>
                            import('@/pages/frontStage/userCenter/refund/apply'),
                        meta: {
                            title: '申请售后(已发货)',
                        },
                    },
                    {
                        path: '/user/refund/unshipped',
                        name: 'unshippedApply',
                        component: () =>
                            import('@/pages/frontStage/userCenter/refund/unshipped'),
                        meta: {
                            title: '申请售后(未发货)',
                        },
                    },
                    {
                        path: '/user/mail',
                        name: 'message',
                        component: () =>
                            import('@/pages/frontStage/userCenter/message/index'),
                        meta: {
                            title: '我的消息',
                        },
                    },
                    {
                        path: '/user/order',
                        name: 'myOrder',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/order'),
                        meta: {
                            title: '我的零星采购订单'
                        },
                    },

                    {
                        path: '/user/blockOrder',
                        name: 'myBlockOrder',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/blockOrder.vue'),
                        meta: {
                            title: '我的大宗临购订单'
                        },
                    },

                    {
                        path: '/user/zzOrder',
                        name: 'myZzOrder',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/zz-order.vue'),
                        meta: {
                            title: '我的周转材料订单'
                        },
                    },
                    {
                        path: '/user/synthesisMaterialOrder',
                        name: 'synthesisMaterialOrder',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/synthesisMaterialOrder.vue'),
                        meta: {
                            title: '我的大宗材料订单'
                        },
                    },
                    {
                        path: '/user/synthesisMaterialDetail',
                        name: 'synthesisMaterialDetail',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/synthesisMaterialDetail.vue'),
                        meta: {
                            title: '大宗材料订单详情'
                        },
                    },
                    {
                        path: '/user/myInvoice',
                        name: 'myInvoice',
                        component: () =>
                            import('@/pages/frontStage/userCenter/invoice/index'),
                        meta: {
                            title: '我的发票',
                        },
                    },
                    {
                        path: '/user/myInvoice/detail',
                        name: 'myInvoiceDetail',
                        component: () =>
                            import('@/pages/frontStage/userCenter/invoice/detail'),
                        meta: {
                            title: '我的发票详情',
                        },
                    },
                    {
                        path: '/user/myInvoice/invoice/company',
                        name: 'companyApply',
                        component: () =>
                            import('@/pages/frontStage/userCenter/invoice/companyApply'),
                        meta: {
                            title: '我的发票-申请发票',
                        },
                    },
                    {
                        path: '/user/changePass',
                        name: 'changePass',
                        component: () =>
                            import('@/pages/frontStage/userCenter/security/changePass'),
                        meta: {
                            title: '修改密码',
                        },
                    },
                    {
                        path: '/user/changePhone',
                        name: 'changePhone',
                        component: () =>
                            import('@/pages/frontStage/userCenter/security/changePhone'),
                        meta: {
                            title: '修改手机号',
                        },
                    },
                    {
                        path: '/user/myInvoice/invoice/individual',
                        name: 'individual',
                        component: () =>
                            import('@/pages/frontStage/userCenter/invoice/individualApply'),
                        meta: {
                            title: '我的发票-个人发票',
                        },
                    },
                    {
                        path: '/user/orderReview',
                        name: 'orderReview',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/review'),
                        meta: {
                            title: '零星采购评价'
                        },
                    },
                    {
                        path: '/user/synthesisMaterialReview',
                        name: 'synthesisMaterialReview',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/synthesisMaterialReview.vue'),
                        meta: {
                            title: '大宗临购评价'
                        },
                    },
                    {
                        path: '/user/circulatingMaterialReview',
                        name: 'circulatingMaterialReview',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/circulatingMaterialReview.vue'),
                        meta: {
                            title: '周转材料评价'
                        },
                    },
                    {
                        path: '/user/followingProduct',
                        name: 'followingProduct',
                        component: () => import('@/pages/frontStage/userCenter/follow/product'),
                        meta: {
                            title: '关注商品'
                        },
                    },
                    {
                        path: '/user/productCompare',
                        name: 'comparison',
                        component: () => import('@/pages/frontStage/userCenter/follow/comparison'),
                        meta: {
                            title: '商品对比历史记录'
                        },
                    },
                    {
                        path: '/user/productCompareDetail',
                        name: 'comparisonDetail',
                        component: () => import('@/pages/frontStage/mall/productComparison/index'),
                        meta: {
                            title: '商品对比详情'
                        },
                    },
                    {
                        path: '/user/changeAvatar',
                        name: 'changeAvatar',
                        component: () => import('@/pages/frontStage/userCenter/user/changeAvatar'),
                        meta: {
                            title: '修改头像昵称'
                        },
                    },
                    {
                        path: '/user/shippingAddr',
                        name: 'shippingAddr',
                        component: () => import('@/pages/frontStage/userCenter/user/shippingAddr'),
                        meta: {
                            title: '设置收货地址'
                        },
                    },
                    {
                        path: '/user/userCart',
                        name: 'shippingAddr',
                        component: () => import('@/pages/frontStage/userCenter/userOrder/cart'),
                        meta: {
                            title: '购物车'
                        },
                    },

                    {
                        path: '/user/verification',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/index'),
                        meta: {
                            title: '认证中心'
                        },
                    },
                    {
                        path: '/user/verification/individual',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/individual'),
                        meta: {
                            title: '认证中心'
                        },
                    },
                    {
                        path: '/user/verification/enterprise',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/enterprise'),
                        meta: {
                            title: '认证中心'
                        },
                    },
                    {
                        path: '/user/reverify/enterprise',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/auditEnterprise'),
                        meta: {
                            title: '修改企业认证'
                        },
                    },
                    {
                        path: '/user/reverify/business',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/auditBusiness'),
                        meta: {
                            title: '修改个体户认证'
                        },
                    },
                    {
                        path: '/user/reverify/individual',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/auditIndividual'),
                        meta: {
                            title: '修改个人认证'
                        },
                    },
                    {
                        path: '/user/verification/detail',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/detail'),
                        meta: {
                            title: '查看详情'
                        },
                    },
                    {
                        path: '/user/verification/enterpriseDetail',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/enterpriseDetail'),
                        meta: {
                            title: '查看企业详情'
                        },
                    },
                    {
                        path: '/user/verification/businessDetail',
                        name: 'verification',
                        component: () => import('@/pages/frontStage/userCenter/verification/businessDetail'),
                        meta: {
                            title: '查看个体户详情'
                        },
                    },
                    {
                        path: '/user/verification/becomeEnterprise',
                        name: 'becomeEnterprise',
                        component: () =>
                            import(
                                '@/pages/frontStage/userCenter/verification/becomeEnterprise'
                            ),
                        meta: {
                            title: '成为企业',
                        },
                    },
                    {
                        path: '/user/verification/becomeBusiness',
                        name: 'becomeBusiness',
                        component: () =>
                            import(
                                '@/pages/frontStage/userCenter/verification/becomeBusiness'
                            ),
                        meta: {
                            title: '成为个体户',
                        },
                    },
                    {
                        path: '/user/myPurchase',
                        name: 'myPurchase',
                        component: () =>
                            import('@/pages/frontStage/userCenter/myPurchase/index.vue'),
                        meta: {
                            title: '我的采购',
                        },
                    },
                    {
                        path: '/user/synthesizeTemporaryPlan',
                        name: 'synthesizeTemporaryPlan',
                        component: () => import('@/pages/frontStage/userCenter/synthesizeTemporaryPlan/index'),
                        meta: {
                            title: '大宗月供计划'
                        }
                    },
                    {
                        path: '/user/synthesizeTemporaryPlanDetail',
                        name: 'synthesizeTemporaryPlanDetail',
                        component: () => import('@/pages/frontStage/userCenter/synthesizeTemporaryPlan/detail'),
                        meta: {
                            title: '大宗月供计划详情'
                        }
                    },
                ],
            },
            {
                path: '/mFront/sheet',
                name: 'sheet',
                component: () =>
                    import('@/pages/frontStage/userCenter/sheet/index.vue'),
                meta: {
                    title: '对账单',
                },
            },
            {
                path: '/mFront/order',
                name: 'frontOrder',
                component: () => import('@/pages/frontStage/userCenter/order/plan/index.vue'),
                meta: {
                    title: '订单中心'
                }
            },
            {
                path: '/user/orderDetail',
                name: 'frontOrder',
                component: () => import('@/pages/frontStage/userCenter/order/detail.vue'),
                meta: {
                    title: '订单中心'
                }
            },
            {
                path: '/mFront/newsList',
                name: 'newsList',
                component: () => import('@/pages/frontStage/mall/news/list'),
                meta: {
                    title: '新闻列表'
                }
            },
            {
                path: '/mFront/newsDetail',
                name: 'newsDetail',
                component: () => import('@/pages/frontStage/mall/news/detail'),
                meta: {
                    title: '新闻详情'
                }
            },
            {
                path: '/user/cart',
                name: 'userCart',
                component: () => import('@/pages/frontStage/userCenter/order/cart'),
                meta: {
                    title: '购物车列表'
                }
            },
            {
                path: '/user/submitCartOrder',
                name: 'submitCartOrder',
                component: () => import('@/pages/frontStage/userCenter/order/submitCartOrder'),
                meta: {
                    title: '购物车结算'
                },
            },
            {
                path: '/user/submitOrder',
                name: 'submitOrder',
                component: () => import('@/pages/frontStage/userCenter/order/submitOrder'),
                meta: {
                    title: '订单结算'
                }
            },
            {
                path: '/mFront/logistic',
                name: 'newsDetail',
                component: () => import('@/pages/frontStage/mall/logistic/index'),
                meta: {
                    title: '物流经营'
                }
            },
            {
                path: '/mFront/logistic',
                name: 'newsDetail',
                component: () => import('@/pages/frontStage/mall/logistic/index'),
                meta: {
                    title: '物流经营'
                }
            },
        ]
    },
    {
        path: '/singleLogin',
        component: () => import('@/pages/frontStage/components/singleLogin'),
    },
    {
        path: '/TTLoginIndex',
        component: () => import('@/pages/frontStage/components/TTLoginIndex'),
    },
    {
        path: '/resetAccount',
        component: () => import('@/pages/frontStage/reset/account'),
    },
    {
        path: '/resetPass',
        component: () => import('@/pages/frontStage/reset/newPass'),
    },
]
