<template>
    <div class="base-page">
      <!-- 列表 -->
      <div class="right">
        <div class="e-table" >
          <div class="top">
            <div class="tabs_div">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="全部" name="all"></el-tab-pane>
                <el-tab-pane label="零星采购" name="first"></el-tab-pane>
                <el-tab-pane label="大宗临购" name="second"></el-tab-pane>
                <el-tab-pane label="周转材料" name="third"></el-tab-pane>
              </el-tabs>
            </div>
                    <div class="left">
                        <div class="left-btn dfa">
                            <el-button type="primary" @click="outputAll">数据导出</el-button>
                            <div class="ml10" style="height: 50px; line-height: 50px;">
                                <el-date-picker
                                    :default-time="['00:00:00', '23:59:59']"
                                    @change="getTableData"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    v-model="filterData.dateScope"
                                    type="datetimerange"
                                    range-separator="至"
                                    :picker-options="pickerOptions"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                >
                                </el-date-picker>
                            </div>
                        </div>
                      <div v-if="tableData && tableData.length > 0" class="ml10">含税：<span style="color: red">{{ tableData[0].countAmount}}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;不含税：<span style="color: red">{{ tableData[0].countNoRateAmount }}</span>
                      </div>
                      <div v-else class="ml10">含税：<span style="color: orangered">0</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;不含税：<span style="color: orangered">0</span></div>
                    </div>
                    <div class="search_box">
                        <el-input clearable style="width: 300px" @blur="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table" :style="{ width: '100%' }">
              <el-table
                v-show="!viewShow && level <= 2 && level >= 0"
                ref="msgTable"
                border
                :data="msgList"
                class="table"
                v-loading="listLoading"
                :height="rightTableHeight"
              >
                <el-table-column align="center" header-align="center" label="序号" type="index" width="60"></el-table-column>
                <el-table-column align="center" header-align="center" :label="level === 0 ?'直属企业' : level === 1 ? '公司名称' : '项目名称'" prop="enterpriseName" width="">
                  <template slot-scope="scope">
                    <span class="action" @click="handleView(scope.row)">{{ scope.row.enterpriseName }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" header-align="center" label="物资类别" prop="productType" width="">
                  <template slot-scope="scope">
                    <span v-if="scope.row.productType===0">低值易耗品</span>
                    <span v-if="scope.row.productType===1">主要材料</span>
                    <span v-if="scope.row.productType===2">周转材料</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" header-align="center" label="订单类型" prop="productType" width="">
                  <template slot-scope="scope">
                    <span v-if="scope.row.productType===0">零星采购订单</span>
                    <span v-if="scope.row.productType===1">大宗临购订单</span>
                    <span v-if="scope.row.productType===2">周转材料订单</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" header-align="center" label="含税交易金额" prop="amount" width=""/>
                <el-table-column align="center" header-align="center" label="不含税交易金额" prop="noRateAmount" width=""/>
                <el-table-column align="center" header-align="center" label="备注" prop="remark" width=""/>
              </el-table>
                <el-table
                    v-show="viewShow && level === 3"
                    ref="eltableCurrentRow2" @row-click="handleCurrentInventoryClick2" @selection-change="selectionChangeHandle" class="table" :height="rightTableHeight" v-loading="tableLoading" :data="tableData" border
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"/>
                    <el-table-column label="订单编号" width="200" prop="orderSn"/>
                    <el-table-column label="供应商" width="200" prop="supplierName"/>
                    <el-table-column label="物资类别" width="260" prop="productType">
                      <template slot-scope="scope">
                        <el-tag v-if="scope.row.productType == 0">低值易耗品</el-tag>
                        <el-tag v-if="scope.row.productType == 1">主要材料</el-tag>
                        <el-tag v-if="scope.row.productType == 2">周转材料</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="物资名称" width="260" prop="productName"/>
                    <el-table-column label="规格型号" width="200" prop="skuName"/>
                    <el-table-column label="计量单位" width="100" prop="unit"/>
                    <el-table-column label="交易数量" width="160" prop="number">
                      <template slot="header">
                        <span>交易数量&nbsp;</span>
                        <el-tooltip
                            effect="light"
                            content="负数为退货数量，正数为发货数量"
                            placement="top"
                        >
                          <img src="@/assets/images/ico_question.png" alt="" style="width: 15px;height: 15px">
                        </el-tooltip>
                      </template>
                    </el-table-column>
<!--                    <el-table-column label="店铺名称" width="200" prop="shopName"/>-->

                    <el-table-column label="含税单价" width="200" prop="productPrice"/>
                    <el-table-column label="不含税单价" width="200" prop="noRatePrice"/>
                    <el-table-column label="含税交易金额" width="" prop="amount"/>
                    <el-table-column label="不含税交易金额" width="" prop="noRateAmount"/>
                    <el-table-column label="客户" width="200" prop="enterpriseName"/>
                    <el-table-column label="交易完成时间" width="160" prop="finishDateStr">
                        <template slot="header">
                            <span>交易完成时间&nbsp;</span>
                            <el-tooltip
                                effect="light"
                                content="收货时间和PCWP退货时间"
                                placement="top"
                            >
                                <img src="@/assets/images/ico_question.png" alt="" style="width: 15px;height: 15px">
                            </el-tooltip>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="结算时间" width="160" prop="finishDate"/>
                    <el-table-column label="已支付金额" width="" prop=""/>
                    <el-table-column label="未支付金额" width="" prop=""/>
                    <el-table-column label="已支付管理费" width="" prop=""/>
                    <el-table-column label="未支付管理费" width="" prop=""/>-->
                </el-table>
              <div style="text-align: right; margin-top: 5px;">
                <el-button type="primary" @click="back" v-if="level > 0">返回</el-button>
              </div>
            </div>
            <Pagination
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="changePageOrSize"
                @sizeChange="changePageOrSize"
            />
        </div>
        <!--        高级查询-->
        <el-dialog title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单编号：" >
                            <el-input clearable maxlength="100" placeholder="请输入订单编号" v-model="filterData.orderSn"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="供应商：">
                      <el-input clearable maxlength="100" placeholder="请输入供应商名称" v-model="filterData.supplierName"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="客户：">
                      <el-input clearable maxlength="100" placeholder="请输入客户名称" v-model="filterData.enterpriseName"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="物资名称：">
                            <el-input clearable maxlength="100" placeholder="请输入物资名称" v-model="filterData.productName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺名称：">
                            <el-input clearable maxlength="100" placeholder="请输入店铺名称" v-model="filterData.shopName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>-->
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="交易完成时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.dateScope"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
<!--                <el-row>
                    <el-col :span="12">
                        <el-form-item label="交易金额以上：">
                            <el-input clearable type="number" v-model="filterData.abovePrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="交易金额以下：">
                            <el-input clearable type="number" v-model="filterData.belowPrice" placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>-->
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import Pagination from '@/components/pagination/pagination'
// eslint-disable-next-line no-unused-vars
// eslint-disable-next-line no-unused-vars
import { debounce } from '@/utils/common'
import { listShipByAffirmList, platformOutputShipExcel, listShipByAffirmOrgList } from '@/api/platform/order/orders'

export default {
    components: {
        Pagination
    },
    watch: {},
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292 - 42
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            sortCode: '',
            activeName: 'all',
            currentQuery: null,
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                },
                    //     {
                    //     text: '最近一年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                    //         start.setHours('00', '00', '00')
                    //         // end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '最近二年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                    //         start.setHours('00', '00', '00')
                    //         // end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '全部',
                    //     onClick (picker) {
                    //         picker.$emit('pick', [])
                    //     }
                    // }
                ]
            },
            // 当前查询
            tableLoading: false,
            listLoading: false,
            // 表格数据
            keywords: null, // 关键字
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 0,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            dataListSelections: [],
            // 高级搜索
            filterData: {
                supplierName: null,
                enterpriseName: null,
                productName: null,
                dateScope: [],
                orderSn: null,
                belowPrice: null,
                abovePrice: null,
                dateValue: [], // 开始时间和结束时间
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            viewShow: false,
            level: 0,
            msgList: [],
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.filterData.dateScope = [this.dateStrM(start), this.dateStrM(end)]
        //this.getTableData()
        this.getTableList()
    },
    methods: {
        // 切换页签
        handleClick (tab, event) {
            console.log(tab, event)
            console.log(this.activeName)
            this.getTableData()
        },
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        // 全部导出
        outputAll () {
            if (this.tableData.length == 0) {
                return this.$message.error('数据为空！')
            }
            if (this.dataListSelections.length != 0) {
                let ids = this.dataListSelections.map(item => {
                    return item.dtlId
                })
                this.currentQuery.ids = ids
                this.tableLoading = true
                platformOutputShipExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '平台交易量信息.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.currentQuery.ids = []
                    this.dataListSelections = []
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            } else {
                this.tableLoading = true
                platformOutputShipExcel(this.currentQuery).then(res => {
                    const blob = new Blob([res], { type: 'application/vnd.ms-excel' }) // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
                    const url = window.URL.createObjectURL(blob) // 3.创建一个临时的url指向blob对象
                    const a = document.createElement('a') // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
                    a.href = url
                    a.download = '平台交易量信息.xlsx'
                    a.click()
                    window.URL.revokeObjectURL(url) // 5.释放这个临时的对象url
                    this.$message.success('操作成功')
                    this.tableLoading = false
                }).catch(() => {
                    this.tableLoading = false
                })
            }
        },
        resetSearchConditions () {
            this.filterData.belowPrice = '' // 以下价格
            this.filterData.abovePrice = '' // 以上价格
            this.filterData.orderSn = ''
            this.filterData.productName = ''
            this.filterData.shopName = ''
            this.filterData.supplierName = ''
            this.filterData.dateValue = []
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        changePageOrSize () {
            if (this.level === 3) {
                this.getTableData()
            } else {
                this.getTableList()
            }
        },
        back () {
            this.listLoading = true
            if(this.level === 3) {// 订单详情
                this.viewShow = false
            }
            if(this.level === 2) {
                this.msgList = [
                    {
                        name: '路面公司',
                        assetType: '1',
                        orderType: '2',
                        numTotalPrice: '2525.00',
                        numTotalNoRatePrice: '2525.00',
                        remark: '无'
                    }
                ]
            }if(this.level === 1) {
                this.msgList = [
                    {
                        name: '路面集团',
                        assetType: '1',
                        orderType: '2',
                        numTotalPrice: '2525.00',
                        numTotalNoRatePrice: '2525.00',
                        remark: '无'
                    }
                ]
            }
            this.level--
            this.paginationInfo.total = 1
            this.paginationInfo.pageSize = 20
            this.paginationInfo.currentPage = 1
            this.listLoading = false
        },
        handleView (row) {
            this.sortCode = row.sortCode
            this.paginationInfo.currentPage = 1
            this.paginationInfo.pageSize = 20
            this.getTableData()
            /*console.log('this.level', this.level, row)
            this.level++
            if (this.level <= 2) {
                // 再设置新的数据
                this.listLoading = true
                if (this.level === 1) {
                    this.msgList = [
                        {
                            name: '路面公司',
                            assetType: '1',
                            orderType: '2',
                            numTotalPrice: '2525.00',
                            numTotalNoRatePrice: '2525.00',
                            remark: '无'
                        }
                    ]
                }else if (this.level === 2) {
                    this.msgList = [
                        {
                            name: '四川省交通建设集团有限责任公司G5京昆高速函',
                            assetType: '1',
                            orderType: '2',
                            numTotalPrice: '2525.00',
                            numTotalNoRatePrice: '2525.00',
                            remark: '无'
                        }
                    ]
                }
                this.paginationInfo.total = 1
                this.paginationInfo.pageSize = 20
                this.paginationInfo.currentPage = 1
                this.listLoading = false
            } else if (this.level === 3) {
                this.viewShow = true
                this.getTableData()
            }*/
        },
        // 高级搜索
        confirmSearch () {
            this.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 点击选中
        handleCurrentInventoryClick2 (row) {
            row.flag = !row.flag
            this.$refs.eltableCurrentRow2.toggleRowSelection(row, row.flag)
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: 1,
                limit: 20,
                productType: this.activeName === 'first' ? 0 : this.activeName === 'second' ? 1 : this.activeName === 'third' ? 2 : null
            }
            if (this.keywords != null && this.keywords != '') {

                params.keywords = this.keywords
            }
            if (this.filterData.dateValue.length != 0 && this.filterData.dateValue != null) {
                params.startFinishDate = this.filterData.dateValue[0]
                params.endFinishDate = this.filterData.dateValue[1]
            }
            if (this.filterData.dateScope.length != 0 && this.filterData.dateScope != null) {
                params.startFinishDate = this.filterData.dateScope[0]
                params.endFinishDate = this.filterData.dateScope[1]
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            if (this.filterData.orderSn != null) {
                params.orderSn = this.filterData.orderSn
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            params.sortCode = this.sortCode
            this.currentQuery = params
            this.tableLoading = true
            this.msgList = []
            listShipByAffirmList(params).then(res => {
                console.log('res', res)
                this.msgList = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            })
        },
        getTableList () {
            this.listLoading = true
            let params = {
                page: 1,
                limit: 20,
                productType: this.activeName === 'first' ? 0 : this.activeName === 'second' ? 1 : this.activeName === 'third' ? 2 : null
            }
            if (this.keywords != null && this.keywords != '') {
                params.keywords = this.keywords
            }
            /*if (this.filterData.dateValue.length != 0 && this.filterData.dateValue != null) {
                params.startFinishDate = this.filterData.dateValue[0]
                params.endFinishDate = this.filterData.dateValue[1]
            }*/
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.supplierName != null) {
                params.supplierName = this.filterData.supplierName
            }
            if (this.filterData.enterpriseName != null) {
                params.enterpriseName = this.filterData.enterpriseName
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if (this.sortCode != null && this.sortCode != '') {
                params.sortCode = this.sortCode
            }
            this.currentQuery = params
            this.listLoading = true
            this.msgList = []
            listShipByAffirmOrgList(params).then(res => {
                console.log('res', res)
                this.msgList = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.listLoading = false
            }).catch(() => {
                this.listLoading = false
            }).finally(() => {
                this.listLoading = false
            })
        },
        // 消息提示
        message (res) {
            if (res.code == 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 200px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}
.base-page .right .top {padding-top: 40px;height: 95px;position: relative;.left {width: 60%;}}
.tabs_div {position: absolute;top: 2px;left: 16px;}
/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
        // bottom: 100px;
    }
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 380px;
        margin-top: 0px;
    }
}

.e-form {
    padding: 0 20px;

    .tabs-title::before {
        content: '';
        height: 22px;
        width: 8px;
        border-radius: 40px;
        background-color: #2e61d7;
        display: block;
        position: absolute;
        left: 20px;
        margin-right: 20px;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-dialog__body {
    margin-top: 0;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
</style>
