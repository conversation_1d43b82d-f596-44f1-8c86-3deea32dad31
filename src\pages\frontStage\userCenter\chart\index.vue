<template>
  <main>
    <div class="msgBox">
      <div class="list-title dfa mb20">
        统计分析
        <div class="filter-options">
          <el-date-picker
              :default-time="['00:00:00', '23:59:59']"
              @change="getTableData"
              value-format="yyyy-MM-dd HH:mm:ss"
              v-model="search.dateScope"
              type="datetimerange"
              range-separator="至"
              :picker-options="pickerOptions"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
          >
          </el-date-picker>
        </div>
        <div class="search df" style="position: absolute; right: 10px">
          <div style="border-radius: 4px;" >
            <el-select  v-model="search.orderType" @change="orderTypeChange()" placeholder="订单类型" clearable >
              <el-option
                  v-for="item in orderTypeOptions"
                  :label="item.name" :value="item.id" :key="item.id"
              />
            </el-select>
          </div>
          <div class="box dfa">
            <img src="@/assets/images/ico_search.png" alt="" />
            <input v-model="search.keywords" type="text" placeholder="模糊搜索" />
          </div>
          <button @click="searchGetList">搜索</button>
        </div>
      </div>
      <div v-show="!viewShow" class="tableBox" style="min-height: 472px;">
        <div class="total" v-if="tableData && tableData.length > 0">不含税合计金额：<span class="amount">{{ tableData[0].countNoRateAmount }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          含税合计金额：<span class="amount">{{ tableData[0].countAmount }}</span>
        </div>
        <div class="total" v-else>不含税合计金额：<span class="amount">0</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          含税合计金额：<span class="amount">0</span>
        </div>
        <el-table
            border ref="msgTable"
            v-loading="listLoading"
            :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
            :row-style="{ fontSize: '14px', height: '48px' }"
            :data="tableData"
        >
          <el-table-column align="center" header-align="center" label="序号" type="index" width="60" :index="indexMethod"></el-table-column>
          <el-table-column align="center" header-align="center" label="机构名称" prop="enterpriseName" width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="action" @click="handleView(scope.row)">{{ scope.row.enterpriseName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" label="物资类别" prop="productType" width="">
            <template slot-scope="scope">
              <span v-if="scope.row.productType===0">低值易耗品</span>
              <span v-if="scope.row.productType===1">主要材料</span>
              <span v-if="scope.row.productType===2">周转材料</span>
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" label="订单类型" prop="productType" width="">
            <template slot-scope="scope">
              <span v-if="scope.row.productType===0">零星采购订单</span>
              <span v-if="scope.row.productType===1">大宗临购订单</span>
              <span v-if="scope.row.productType===2">周转材料订单</span>
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" label="含税交易金额" prop="amount" width=""/>
          <el-table-column align="center" header-align="center" label="不含税交易金额" prop="noRateAmount" width=""/>
          <el-table-column align="center" header-align="center" label="备注" prop="remark" width=""/>
        </el-table>
        <div style="text-align: right; margin-top: 5px;">
          <el-button type="primary" @click="back" v-if="sortCode !== ''">返回</el-button>
        </div>
      </div>
      <div v-show="viewShow" class="tableBox" style="min-height: 472px;">
        <div v-if="orderList && orderList.length > 0" class="total">不含税合计金额：<span class="amount">{{ orderList[0].countAmount }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          含税合计金额：<span class="amount">{{ orderList[0].countNoRateAmount }}</span></div>
        <div v-else class="total">不含税合计金额：<span class="amount">0</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          含税合计金额：<span class="amount">0</span></div>
        <el-table
            border ref="orderList"
            :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
            :row-style="{ fontSize: '14px', height: '48px' }"
            :data="orderList"
            v-loading="tableLoading"
        >
          <el-table-column align="center" header-align="center" label="序号" type="index" width="60" :index="indexMethod"></el-table-column>
          <el-table-column align="center" header-align="center" label="订单编号" prop="orderSn" width="" :show-overflow-tooltip="true"/>
          <el-table-column align="center" header-align="center" label="供应商" prop="supplierName" width="" :show-overflow-tooltip="true"/>
          <el-table-column align="center" header-align="center" label="物资类别" prop="productType" width="" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span v-if="scope.row.productType==='10'">低值易耗品</span>
              <span v-if="scope.row.productType==='12'">主要材料</span>
              <span v-if="scope.row.productType==='13'">周转材料</span>
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" label="物资名称" prop="productName" width="" :show-overflow-tooltip="true"/>
          <el-table-column align="center" header-align="center" label="规格型号" prop="skuName" width="" :show-overflow-tooltip="true"/>
          <el-table-column align="center" header-align="center" label="计量单位" prop="unit" width=""/>
          <el-table-column align="center" header-align="center" label="交易数量" prop="number" width=""/>
          <el-table-column align="center" header-align="center" label="含税单价" prop="productPrice" width=""/>
          <el-table-column align="center" header-align="center" label="不含税单价" prop="noRatePrice" width=""/>
          <el-table-column align="center" header-align="center" label="含税交易金额" prop="amount" width=""/>
          <el-table-column align="center" header-align="center" label="不含税交易金额" prop="noRateAmount" width=""/>
          <el-table-column align="center" header-align="center" label="客户" prop="enterpriseName" width="" :show-overflow-tooltip="true"/>
          <el-table-column align="center" header-align="center" label="交易时间" prop="finishDateStr" width="" :show-overflow-tooltip="true"/>
        </el-table>
        <div style="text-align: right; margin-top: 5px;">
          <el-button type="primary" @click="back" v-if="sortCode !== ''">返回</el-button>
        </div>
      </div>
      <pagination
          :currentPage.sync="page.currPage"
          :destination="page.destination"
          :total="page.totalCount"
          :pageSize="page.pageSize"
          :totalPage="page.totalPage"
          @currentChange="currentChange"
          @sizeChange="sizeChange"
      ></pagination>
    </div>
  </main>
</template>
<script>
import pagination from '../../components/pagination.vue'
import { listShipByAffirmList, listShipByAffirmOrgList } from '@/api/platform/order/orders'

export default {
    components: { pagination },
    data () {
        return {
            sortCode: '',
            pickerOptions: {
                shortcuts: [{
                    text: '最近一个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                }, {
                    text: '最近半年',
                    onClick (picker) {
                        const end = new Date()
                        const start = new Date()
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
                        start.setHours('00', '00', '00')
                        // end.setHours('23', '59', '59')
                        picker.$emit('pick', [start, end])
                    }
                },
                    //     {
                    //     text: '最近一年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360)
                    //         start.setHours('00', '00', '00')
                    //         // end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '最近二年',
                    //     onClick (picker) {
                    //         const end = new Date()
                    //         const start = new Date()
                    //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 360 * 2)
                    //         start.setHours('00', '00', '00')
                    //         // end.setHours('23', '59', '59')
                    //         picker.$emit('pick', [start, end])
                    //     }
                    // }, {
                    //     text: '全部',
                    //     onClick (picker) {
                    //         picker.$emit('pick', [])
                    //     }
                    // }
                ]
            },
            orderTypeOptions: [
                {
                    id: '1',
                    name: '零星采购订单'
                },
                {
                    id: '2',
                    name: '大宗临购订单'
                },
                {
                    id: '3',
                    name: '周转材料订单'
                }
            ],
            page: {
                totalPage: 0,
                totalCount: 0,
                currPage: 1,
                pageSize: 10,
                destination: null
            },
            viewShow: false,
            template: {
                enterprise: '路面集团',
                company: '路面公司',
                project: '四川省交通建设集团有限责任公司G5京昆高速函',
                assetType: '1',
                orderType: '2',
                numTotalPrice: '2525.00',
                numTotalNoRatePrice: '2525.00',
                remark: '无'
            },
            msgList: [],

            search: {
                dateScope: [],
                orderType: '',
                keywords: ''
            },
            level: 0,
            total: '2532726.90',
            totalRate: '2444444.00',
            orderList: [],
            tableData: [],
            tableLoading: false,
            listLoading: false
        }
    },
    created () {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        start.setHours('00', '00', '00')
        this.search.dateScope = [this.dateStrM(start), this.dateStrM(end)]
        this.getTableList()
    },
    methods: {
        // 日期处理
        padZero (num) {
            return num < 10 ? `0${num}` : num
        },
        dateStrM (date) {
            const year = date.getFullYear()
            const month = this.padZero(date.getMonth() + 1)
            const day = this.padZero(date.getDate())
            const hour = this.padZero(date.getHours())
            const minute = this.padZero(date.getMinutes())
            const second = this.padZero(date.getSeconds())
            return `${year}-${month}-${day} ${hour}:${minute}:${second}`
        },
        back () {
            this.viewShow = false
            this.page.pageSize = 20
            this.page.currPage = 1
            //this.sortCode = this.sortCode.split('!').slice(0, -1).join('!')
            this.sortCode = this.tableData[0].parentSortCode
            this.getTableList()
        },
        indexMethod (index) {
            return this.page.pageSize * (this.page.currPage - 1) + index + 1
        },
        handleView (row) {
            this.sortCode = row.sortCode
            this.page.currPage = 1
            this.page.pageSize = 20
            if (row.orglayertypenumber === '9') {
                this.viewShow = true
                this.getTableData()
            } else {
                this.getTableList()
            }
        },
        searchGetList () {
            if (this.viewShow) {
                this.getTableData()
            } else {
                this.getTableList()
            }
        },
        // 订单类型变更
        orderTypeChange () {

        },
        // 页码变更
        currentChange (currPage) {
            this.page.currPage = currPage
            if (this.viewShow) {
                this.getTableData()
            } else {
                this.getTableList()
            }
        },
        sizeChange (pageSize) {
            this.page.pageSize = pageSize
            if (this.viewShow) {
                this.getTableData()
            } else {
                this.getTableList()
            }
        },
        // 获取表格数据
        getTableData () {
            let params = {
                page: this.page.currPage,
                limit: this.page.pageSize,
            }
            if (this.search.keywords != null && this.search.keywords != '') {
                params.keywords = this.search.keywords
            }
            if (this.search.dateScope.length != 0 && this.search.dateScope != null) {
                params.startFinishDate = this.search.dateScope[0]
                params.endFinishDate = this.search.dateScope[1]
            }
            params.sortCode = this.sortCode
            this.tableLoading = true
            this.orderList = []
            listShipByAffirmList(params).then(res => {
                this.orderList = res.list
                /*this.orderList.forEach(item => {
                    item.customer = name
                })*/
                this.page.totalCount = res.totalCount
                this.page.totalPage = res.totalPage
                this.page.pageSize = res.pageSize
                this.page.currPage = res.currPage
                this.tableLoading = false
            }).catch(() => {
                this.tableLoading = false
            }).finally(() => {
                this.tableLoading = false
            })
        },
        getTableList () {
            let params = {
                page: this.page.currPage,
                limit: this.page.pageSize
            }
            if (this.search.keywords != null && this.search.keywords != '') {
                params.keywords = this.search.keywords
            }
            if (this.search.dateScope.length != 0 && this.search.dateScope != null) {
                params.startFinishDate = this.search.dateScope[0]
                params.endFinishDate = this.search.dateScope[1]
            }
            if (this.sortCode != null && this.sortCode != '') {
                params.sortCode = this.sortCode
            }
            this.listLoading = true
            this.tableData = []
            listShipByAffirmOrgList(params).then(res => {
                this.tableData = res.list
                this.page.totalCount = res.totalCount
                this.page.pageSize = res.pageSize
                this.page.currPage = res.currPage
                this.listLoading = false
            }).catch(() => {
                this.listLoading = false
            }).finally(() => {
                this.listLoading = false
            })
        },
    },
}
</script>
<style scoped lang="scss">

main {
  padding: 0 20px;
}
.list-title {
  padding: 0;
  position: relative;
  .filter-options {
    position: absolute;
    left: 100px;
  }
  .pointer {
    font-size: 4px;
    color: rgba(33, 110, 198, 1);
    position: absolute;
    right: 20px;
  }
}
.search {
  .box {
    width: 168px;
    height: 32px;
    border: 1px solid rgba(229, 229, 229, 1);
    border-right: 0;
    border-radius: 4px;

    img {
      width: 16px;
      height: 16px;
      margin: 0 4px 0 10px;
    }

    input {
      width: 130px;
    }
  }

  button {
    width: 52px;
    height: 32px;
    font-size: 14px;
    color: #fff;
    background-color: rgba(212, 48, 48, 1);
  }
}
.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}
.total {
  padding-left: 10px;
  padding-bottom: 10px;
  font-size: 16px;
}
.amount {
  color: rgba(212, 48, 48, 1);
}
</style>
