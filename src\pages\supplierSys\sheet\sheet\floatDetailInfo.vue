<template>
        <div class="e-form">
            <!--浮动-->
            <BillTop @cancel="handleClose"></BillTop>
            <div class="tabs warningTabs" v-loading="formLoading">
                <el-tabs tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                    <el-tab-pane label="对账单详情" name="baseInfo" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="对账单明细" name="reconciliationDtl" :disabled="clickTabFlag">
                    </el-tab-pane>
                    <el-tab-pane label="审核历史" name="auditRecords" :disabled="clickTabFlag"/>
                    <div id="tabs-content">
                        <!-- 基本信息 -->
                        <div id="baseInfo" class="con">
                            <div class="tabs-title" id="baseInfo">对账单详情</div>
                            <el-form
                                :model="reconciliationForm" label-width="200px" :rules="reconciliationFormRules"
                                ref="reconciliationFormRef" :disabled="false" class="demo-ruleForm"
                            >
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账单编号：" prop="reconciliationNo">
                                            <span>{{ reconciliationForm.reconciliationNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="创建时间：" prop="gmtCreate">
                                            <span>{{ reconciliationForm.gmtCreate }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="业务类型：" prop="reconciliationProductType">
                                            <el-tag v-if="reconciliationForm.reconciliationProductType == 0 || reconciliationForm.reconciliationProductType == 10">零星采购</el-tag>
                                            <el-tag v-if="reconciliationForm.reconciliationProductType == 1 || reconciliationForm.reconciliationProductType == 13">大宗临购</el-tag>
                                            <el-tag v-if="reconciliationForm.reconciliationProductType == 2">周转材料</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" v-if="reconciliationForm.reconciliationProductType === 12">
                                        <el-form-item label="合同编号：" prop="sourceBillNo">
                                            <span>{{ reconciliationForm.sourceBillNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" v-if="reconciliationForm.reconciliationProductType === 10">
                                        <el-form-item label="计划编号：" prop="sourceBillNo">
                                            <span>{{ reconciliationForm.sourceBillNo }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账类型：" prop="type">
                                            <el-tag v-if="reconciliationForm.type == 1">浮动价格对账</el-tag>
                                            <el-tag v-else-if="reconciliationForm.type == 2">固定价格对账</el-tag>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="状态：" prop="state">
                                            <el-tag type="info" v-if="reconciliationForm.state == 0">草稿</el-tag>
                                            <el-tag v-else-if="reconciliationForm.state == 1">已提交</el-tag>
                                            <el-tag v-else-if="reconciliationForm.state == 2">待审核</el-tag>
                                            <el-tag
                                                type="success" v-else-if="reconciliationForm.state == 3"
                                            >审核通过
                                            </el-tag>
                                            <el-tag type="danger" v-else-if="reconciliationForm.state == 4">审核失败</el-tag>
                                            <el-tag type="danger" v-else-if="reconciliationForm.state == 7">已作废</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="供货单位：" prop="supplierName">
                                            <span>{{ reconciliationForm.supplierName }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="收货单位：" prop="purchasingOrgName">
                                            <span>{{ reconciliationForm.purchasingOrgName }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="含税总金额：" prop="reconciliationAmount">
                                            <span>{{ reconciliationForm.reconciliationAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="对账总金额（不含税）：" prop="reconciliationNoRateAmount">
                                            <span>{{ reconciliationForm.reconciliationNoRateAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="税率（%）：" prop="taxRate">
                                            <el-input
                                                v-if="reconciliationForm.state === 0 || reconciliationForm.state === 1 || reconciliationForm.state === 4"
                                                type="number"
                                                v-model="reconciliationForm.taxRate"
                                                @change="taxRateChange()">
                                            </el-input>
                                            <span v-else>{{ reconciliationForm.taxRate }}%</span>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="已结算金额：" prop="settleAmount">
                                            <span>{{ reconciliationForm.settleAmount }}</span>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                              <el-row>
                                <el-col :span="12">
                                  <el-form-item label="税额：" prop="taxAmount">
                                    <span>{{reconciliationForm.taxAmount}}</span>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="对账时间：">
                                            <el-date-picker
                                                style="width: 300px"
                                                disabled
                                                value-format="yyyy-MM-dd"
                                                v-model="reconciliationForm.startEndTme"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="新增来源：" prop="type">
                                            <el-tag v-if="reconciliationForm.createType == 1">收货单位新增</el-tag>
                                            <el-tag v-else-if="reconciliationForm.createType == 2">供货单位新增</el-tag>
                                            <el-tag v-else-if="reconciliationForm.createType == 3">PCWP推送</el-tag>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="备注：" prop="remarks">
                                            <el-input
                                                style="min-width: 500px;" type="textarea" :auto-resize="false"
                                                v-model="reconciliationForm.remarks"
                                                placeholder="请输入备注" maxlength="1000" show-word-limit
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </el-form>
                        </div>
                        <!--计划清单-->
                        <div id="reconciliationDtl" class="con">
                          <reconciliationDtl-From :tableData="tableData" :reconciliationForm="reconciliationForm" :type="1">
                          </reconciliationDtl-From>
<!--                            <div class="tabs-title" id="reconciliationDtl">对账单明细</div>-->
<!--                            <div class="e-table" style="background-color: #fff">-->
<!--                                <el-table-->
<!--                                    ref="tableRef"-->
<!--                                    border-->
<!--                                    :data="tableData"-->
<!--                                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"-->
<!--                                    :row-style="{ fontSize: '14px', height: '48px' }"-->
<!--                                >-->
<!--                                    <el-table-column label="序号" type="index" width="60"></el-table-column>-->
<!--                                    <el-table-column prop="receivingDate" label="收料日期" width="130">-->
<!--                                        <template v-slot="scope">-->
<!--                                            {{ scope.row.receivingDate | dateStr }}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column-->
<!--                                        label="操作" width="120" fixed="left"-->
<!--                                        v-if="reconciliationForm.reconciliationProductType == 12 && [0, 4].includes(reconciliationForm.state) && reconciliationForm.createType != 3"-->
<!--                                    >-->
<!--                                        <template v-slot="scope">-->
<!--                                            <span-->
<!--                                                class="pointer" style="color: rgba(33, 110, 198, 1); margin-left: 20px"-->
<!--                                                @click="dismantleM(scope.row)"-->
<!--                                            >拆单</span>-->
<!--                                            <span-->
<!--                                                v-if="scope.row.reconciliationDtlId == null" class="pointer"-->
<!--                                                style="color: rgb(176,5,5); margin-left: 20px"-->
<!--                                                @click="deleteM(scope.row)"-->
<!--                                            >删除</span>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column prop="orderSn" label="订单号" width="220"/>-->
<!--&lt;!&ndash;                                    零星采购计划pcwp的材质是物资的商品名称&ndash;&gt;-->
<!--                                    <el-table-column v-if="reconciliationForm.reconciliationProductType===10" prop="texture" label="商品名称" width="200"/>-->
<!--                                    <el-table-column prop="materialName" label="物资名称" width="200"/>-->
<!--                                    <el-table-column prop="spec" label="规格型号"/>-->
<!--                                    <el-table-column prop="unit" label="单位"/>-->
<!--                                    <el-table-column v-if="reconciliationForm.reconciliationProductType===12" prop="texture" label="材质" width=""/>-->
<!--                                    <el-table-column prop="maxQuantity" label="可选数量" width="70"/>-->
<!--                                    <el-table-column prop="quantity" label="已选数量" width="100">-->
<!--                                        <template v-slot="scope">-->
<!--                                            <el-input-->
<!--                                                :disabled="reconciliationForm.createType == 3"-->
<!--                                                v-if="[0, 1, 4].includes(reconciliationForm.state)"-->
<!--                                                type="number"-->
<!--                                                v-model="scope.row.quantity"-->
<!--                                                @change="getChangedRow(scope.row)"-->
<!--                                            >-->
<!--                                            </el-input>-->
<!--                                            <span v-else>{{ scope.row.quantity }}</span>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column prop="freightPrice" label="到货网价" width="100">-->
<!--                                        <template v-slot="scope">-->
<!--                                            <el-input-->
<!--                                                :disabled="reconciliationForm.createType == 3"-->
<!--                                                v-if="[0, 1, 4].includes(reconciliationForm.state)"-->
<!--                                                type="number"-->
<!--                                                v-model="scope.row.freightPrice"-->
<!--                                                @change="priceChange(scope.row)"-->
<!--                                            >-->
<!--                                            </el-input>-->
<!--                                            <span v-else>{{ scope.row.freightPrice }}</span>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column prop="fixationPrice" label="固定费用" width="100">-->
<!--                                        <template v-slot="scope">-->
<!--                                            <el-input-->
<!--                                                :disabled="reconciliationForm.createType == 3"-->
<!--                                                v-if="[0, 1, 4].includes(reconciliationForm.state)"-->
<!--                                                type="number"-->
<!--                                                v-model="scope.row.fixationPrice"-->
<!--                                                @change="priceChange(scope.row)"-->
<!--                                            >-->
<!--                                            </el-input>-->
<!--                                            <span v-else>{{ scope.row.fixationPrice }}</span>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column prop="price" label="含税单价"/>-->
<!--                                    <el-table-column prop="noRatePrice" label="不含税单价"/>-->
<!--                                    <el-table-column prop="acceptanceAmount" label="含税总金额"/>-->
<!--                                    <el-table-column prop="acceptanceNoRateAmount" label="不含税总金额"/>-->
<!--                                    <el-table-column prop="settledAmount" label="已结算金额"/>-->
<!--                                    <el-table-column prop="remarks" label="备注">-->
<!--                                        <template v-slot="scope">-->
<!--                                            <el-input-->
<!--                                                :disabled="reconciliationForm.createType == 3"-->
<!--                                                v-if="[0, 1, 4].includes(reconciliationForm.state)"-->
<!--                                                v-model="scope.row.remarks"-->
<!--                                                @change="getChangedRow(scope.row)"-->
<!--                                            >-->
<!--                                            </el-input>-->
<!--                                            <span v-else>{{ scope.row.remarks }}</span>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                </el-table>-->
<!--                            </div>-->
                        </div>
                        <div id="auditRecords" class="con">
                          <audit-from :auditRecords="reconciliationForm.auditRecords"></audit-from>
                        </div>
                    </div>
                </el-tabs>
                <div class="buttons">
                    <el-button
                        type="primary"
                        @click="outPutExcelM"
                    >导出
                    </el-button>
                    <el-button
                        type="primary"
                        v-if="(reconciliationForm.state === 0 || reconciliationForm.state === 4)
                        &&(reconciliationForm.createType === 2 || reconciliationForm.createType === 3)"
                        @click="saveSheetM(0)"
                    >保存
                    </el-button>
                    <el-button
                        type="primary"
                        class="btn-greenYellow"
                        v-if="(reconciliationForm.state === 0 || reconciliationForm.state === 4)
                        &&(reconciliationForm.createType === 2 || reconciliationForm.createType === 3)"
                        @click="saveSheetM(1)"
                    >保存并提交
                    </el-button>
                    <el-button
                        type="primary"
                        class="btn-greenYellow" :disabled="disabledElement"
                        v-if="reconciliationForm.state === 2  && userInfo.roles.includes('物资对账单审核')
                        &&(reconciliationForm.createType === 2 || reconciliationForm.createType === 3)"
                        @click="auditPlanM(1, '通过')"
                    >通过
                    </el-button>
                    <el-button
                        type="primary" class="btn-delete" :disabled="disabledElement"
                        v-if="reconciliationForm.state === 2 && userInfo.roles.includes('物资对账单审核')
                        &&(reconciliationForm.createType === 2 || reconciliationForm.createType === 3)"
                        @click="auditPlanM(0, '未通过')"
                    >未通过
                    </el-button>
                    <el-button
                        type="primary" class="btn-greenYellow" :disabled="disabledElement"
                        v-if="reconciliationForm.state === 3&&showDevFunc&&(reconciliationForm.invoiceState===0||reconciliationForm.invoiceState===3)"
                        @click="createInvoice"
                    >开票</el-button>
                    <el-button
                        type="primary" class="btn-delete"
                        v-if="(reconciliationForm.state === 0 || reconciliationForm.state === 1 || reconciliationForm.state === 2)
                        &&(reconciliationForm.createType === 2 || reconciliationForm.createType === 3)
                        &&(reconciliationForm.invoiceState==0||reconciliationForm.invoiceState==3||reconciliationForm.invoiceState==5||reconciliationForm.invoiceState==7)"
                        @click="cancellationM"
                    >作废
                    </el-button>
                    <!-- <el-button
                        type="primary"
                        v-if="reconciliationForm.state === 3 && reconciliationForm.supplierIsAffirm === 0"
                        @click="affirmM"
                    >确认
                    </el-button> -->
                    <el-button
                        type="primary" class="btn-delete"
                        v-if="(reconciliationForm.state === 0 || reconciliationForm.state === 1 || reconciliationForm.state === 2)
                        &&(reconciliationForm.createType === 2 || reconciliationForm.createType === 3)"
                        @click="deleteOneM"
                    >删除
                    </el-button>
                    <el-button @click="handleClose">返回</el-button>
                </div>
            </div>
            <el-dialog
                v-dialogDrag title="选择合同" :visible.sync="showSelectContractOrPlan" width="80%" style="margin-left: 10%;"
                :close-on-click-modal="false"
            >
                <div class="e-table" style="background-color: #fff" v-loading="selectContractOrPlanLoading">
                    <div class="top" style="height: 50px; padding-left: 10px">
                        <div class="left">
                            <el-input
                                type="text" @blur="getContractOrPlanListM" placeholder="输入搜索关键字" v-model="keywords"
                            >
                                <img :src="require('@/assets/search.png')" slot="suffix" @click="getContractOrPlanListM"/>
                            </el-input>
                            <div class="search_box" style="margin-left: 10px">
                            </div>
                        </div>
                    </div>
                    <el-table
                        ref="bidingOrderItemRef"
                        border
                        :data="contractOrPlanTableDate"
                        class="table"
                    >
                        <el-table-column type="selection" width="40"></el-table-column>
                        <el-table-column label="序号" type="index" width="60"></el-table-column>
                        <el-table-column prop="contractNo" label="合同编号"></el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <Pagination
                    :total="paginationInfo.total"
                    :pageSize.sync="paginationInfo.pageSize"
                    :currentPage.sync="paginationInfo.currentPage"
                    @currentChange="currentChangeUser"
                    @sizeChange="sizeChangeUser"
                />
                <div class="buttons">
                    <el-button @click="showSelectContractOrPlan = false">关闭</el-button>
                </div>
            </el-dialog>
        </div>
</template>
<script>
import $ from 'jquery'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
// eslint-disable-next-line no-unused-vars
import { calculateNotTarRateAmount, getUuid, throttle } from '@/utils/common'
import {
    getContactPlanPageList,
    materialReconciliationCancellation,
    materialReconciliationDelete,
    materialReconciliationGetBySn,
    materialReconciliationSubmit,
    materialReconciliationSupplierAffirm,
    materialReconciliationSupplierUpdate,
    outputExcel
} from '@/api/reconciliation/reconciliation'
import { mapState } from 'vuex'
import AuditFrom from '@/components/reconciliation/auditFrom.vue'
import ReconciliationDtlFrom from '@/components/reconciliation/reconciliationDtlFrom.vue'
import {
    reconciliationFloatCountAmount
} from '@/utils/material_reconciliationUtils/compute'

export default {
    filters: {
        dateStr (dateStr) {
            if (dateStr == null) {
                return
            }
            return  dateStr.split(' ')[0]
        }
    },
    components: { ReconciliationDtlFrom, AuditFrom },
    data () {
        return {
            reconciliationFormRules: {
                startEndTme: [
                    { required: true, message: '请选择对账时间', trigger: 'blur' }
                ],
                reconciliationProductType: [
                    { required: true, message: '请选择业务类型', trigger: 'blur' }
                ]
            },
            formLoading: false,
            yyyymmdd: '',
            productType: null,
            selectContractOrPlanLoading: false,
            showSelectContractOrPlan: false,
            contractOrPlanTableDate: [],
            keywords: null,
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1
            },
            reconciliationForm: {
                type: null,
                orderId: null,
                orderSn: null,
                businessType: null,
                reconciliationProductType: null,
                sourceBillId: null,
                sourceBillNo: null,
                supplierId: null,
                supplierEnterpriseId: null,
                supplierName: null,
                purchaserId: null,
                purchaserLocalId: null,
                purchaserName: null,
                purchasingOrgId: null,
                purchasingLocalOrgId: null,
                purchasingOrgName: null,
                acceptanceName: null,
                reconciliationAmount: null,
                taxAmount: null,
                reconciliationNoRateAmount: null,
                settleAmount: null,
                startTime: null,
                endTime: null,
                startEndTme: [],
                createType: null
                // TODO 待完善

            },
            showReconciliationForm: false,
            reconciliationFormLoading: false,
            maxNum: *********,
            tableData: [],
            tabsName: 'baseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            tableLoading: false,
            changedRow: []
        }
    },
    created () {
        this.materialReconciliationGetBySnM()
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 140 + 'px'
        },
        // 填补底部空白，以使高度够滚动
        seatHeight () {
            return this.screenHeight - 72 - this.lastConHeight
        }
    },
    watch: {
        /*screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        }*/
    },
    methods: {
        submitSheetM () {
            this.clientPop('info', '您确定要提交数据吗？', async () => {
                this.tableLoading = true
                materialReconciliationSubmit([this.reconciliationForm.reconciliationId]).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.materialReconciliationGetBySnM()
                    }
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        outPutExcelM () {
            this.formLoading = true
            const now = this.yyyymmdd
            const date = now.getDate()  // 获取月份，返回值范围是 0-11，因此需要加 1
            const month = now.getMonth() + 1 // 获取月份，返回值范围是 0-11，因此需要加 1
            const year = now.getFullYear()
            outputExcel({ reconciliationId: this.reconciliationForm.reconciliationId }).then(res => {
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                if (this.reconciliationForm.type == 1) {
                    a.download = year + '年' + month + '月' + date + '日' + this.reconciliationForm.purchasingOrgName + '浮动价格对账单.xlsx'
                }
                if (this.reconciliationForm.type == 2) {
                    a.download = year + '年' + month + '月' + date + '日' + this.reconciliationForm.purchasingOrgName + '固定价格对账单.xlsx'
                }
                a.click()
                window.URL.revokeObjectURL(url)
                this.$message.success('操作成功')
            }).finally(() => {
                this.formLoading = false
            })
        },
        cancellationM () {
            this.clientPop('info', '您确定要作废数据吗？', async () => {
                this.formLoading = true
                materialReconciliationCancellation({ reconciliationId: this.reconciliationForm.reconciliationId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        createInvoice () {
            let reconciliationIds = []
            reconciliationIds.push(this.reconciliationForm.reconciliationId)
            this.$router.push({
                path: '/supplierSys/supplierApply/invoice/apply',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'supplierSysApply',
                params: {
                    row: {
                        reconciliationIds: reconciliationIds,
                        enterpriseId: this.reconciliationForm.purchasingOrgId,
                        taxRate: this.reconciliationForm.taxRate
                    }
                }
            })
        },
        affirmM () {
            this.clientPop('info', '您确定要确认数据吗？', async () => {
                this.formLoading = true
                materialReconciliationSupplierAffirm({ reconciliationId: this.reconciliationForm.reconciliationId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        deleteOneM () {
            this.clientPop('info', '您确定要删除数据吗？', async () => {
                this.formLoading = true
                materialReconciliationDelete({ reconciliationId: this.reconciliationForm.reconciliationId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.formLoading = false
                })
            })
        },
        materialReconciliationGetBySnM () {
            this.formLoading = true
            materialReconciliationGetBySn({ sn: this.$route.query.sn }).then(res => {
                if (res != null) {
                    this.tableData = res.dtl
                    this.reconciliationForm = res
                    const timeArr = []
                    this.reconciliationForm.startEndTme = [res.startTime, res.endTime]
                    for (let i = 0; i < this.tableData.length; i++) {
                        this.tableData[i].maxQuantity = this.tableData[i].sourceQuantity
                        timeArr.push(new Date(this.tableData[i].receivingDate))
                        this.tableData[i].uuid = getUuid()
                        this.tableData[i].groupUuid = getUuid()
                    }
                    this.yyyymmdd = new Date(Math.max.apply(null, timeArr))
                }
            }).finally(() => {
                this.formLoading = false
            })
        },
        currentChangeUser (currPage) {
            this.paginationInfo.currentPage = currPage
            this.getContractOrPlanListM()
        },
        sizeChangeUser (pageSize) {
            this.paginationInfo.pageSize = pageSize
            this.getContractOrPlanListM()
        },
        getContractOrPlanListM () {
            let params = {
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize
            }
            if (this.keywords != null) {
                params.keywords = this.keywords
            }
            if (this.productType != null) {
                params.productType = this.productType
            }
            this.selectContractOrPlanLoading = true
            getContactPlanPageList(params).then(res => {
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
                this.contractOrPlanTableDate = res.list
            }).finally(() => {
                this.selectContractOrPlanLoading = false
            })
        },
        saveSheetM (num) {
            if (this.tableData.length === 0) {
                return this.$message.error('对账明细不能为空！')
            }
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.quantity <= 0) {
                    return this.$message.error('序号为：【' + (i + 1) + '】的数量需要大于0！')
                }
                if (t.freightPrice <= 0) {
                    return this.$message.error('序号为：【' + (i + 1) + '】的到货网价需要大于0！')
                }
            }
            this.reconciliationForm.dtl = this.tableData
            this.$refs.reconciliationFormRef.validate(valid => {
                if (valid) {
                    this.clientPop('info', '您确定要操作吗！', async () => {
                        this.formLoading = true
                        // 处理日期
                        this.reconciliationForm.startTime = this.reconciliationForm.startEndTme[0]
                        this.reconciliationForm.endTime = this.reconciliationForm.startEndTme[1]
                        this.reconciliationForm.isSubmit = num
                        materialReconciliationSupplierUpdate(this.reconciliationForm).then(res => {
                            if (res.code != null && res.code == 200) {
                                this.$message.success('操作成功')
                                this.materialReconciliationGetBySnM()
                            }
                        }).finally(() => {
                            this.formLoading = false
                        })
                    })
                }
            })
        },
        // 选择合同
        selectContractClick () {
            this.getContractOrPlanListM()
            this.showSelectContractOrPlan = true
        },
        // 删除单
        deleteM (row) {
            this.tableData = this.tableData.filter(t => {
                if (t.uuid != row.uuid) {
                    return true
                } else {
                    return false
                }
            })
        },
        // 拆单
        dismantleM (row) {
            // 插入到当前点击的下一个节点
            let insertIndex = this.tableData.length
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.uuid == row.uuid) {
                    insertIndex = i + 1
                }
            }
            let newRow = {
                ...row,
                reconciliationDtlId: null, // 拆单不能存放id，用于后端保存
                acceptanceAmount: this.fixed2(0),
                quantity: 0,
                uuid: getUuid()
            }
            this.tableData.splice(insertIndex, 0, newRow)
        },
        taxRateChange () {
            if(this.reconciliationForm.taxRate == null || this.reconciliationForm.taxRate < 0) {
                this.reconciliationForm.taxRate = this.fixed2(0)
            }else if(this.reconciliationForm.taxRate >= 100 ) {
                this.reconciliationForm.taxRate = 100
            }else {
                this.reconciliationForm.taxRate = this.fixed2(this.reconciliationForm.taxRate)
            }
            // 计算金额
            this.countAmountM()
        },
        priceChange (row) {
            // 一旦变化则是1
            row.updateType = 1
            // 处理固定费用
            this.disposeFixationPriceM(row)
            // 处理到店网价
            this.disposeFreightPriceM(row)
            // 计算金额
            this.countAmountM()
        },
        // 表单变化
        getChangedRow (row) {
            // 处理数量
            this.disposeQuantityM(row)
            // 计算金额
            this.countAmountM()
        },
        countAmountM () {
            let params = reconciliationFloatCountAmount(this.tableData, this.reconciliationForm.taxRate)
            this.tableData = params.tableData
            this.reconciliationForm.reconciliationAmount = params.reconciliationAmount
            this.reconciliationForm.reconciliationNoRateAmount = params.reconciliationNoRateAmount        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        // 处理固定费用
        disposeFixationPriceM (row) {
            if (row.fixationPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.fixationPrice = this.fixed2(0)
            } else {
                row.fixationPrice = this.fixed2(row.fixationPrice)
            }
        },
        // 处理到货网价
        disposeFreightPriceM (row) {
            if (row.freightPrice <= 0 || row.freightPrice >= this.maxNum) {
                row.freightPrice = this.fixed2(0)
            } else {
                row.freightPrice = this.fixed2(row.freightPrice)
            }
        },
        // 处理数量
        disposeQuantityM (row) {
            if (row.quantity <= 0) {
                return row.quantity = this.fixed4(0)
            }
            // 计算最大值
            let maxNum = this.fixed4(row.maxQuantity)
            let countNum = 0
            for (let i = 0; i < this.tableData.length; i++) {
                let t = this.tableData[i]
                if (t.materialName === row.materialName && t.orderSn === row.orderSn && t.spec === row.spec && t.unit == row.unit && t.uuid !== row.uuid && row.groupUuid === t.groupUuid) {
                    countNum++
                    if (maxNum <= 0) {
                        maxNum = 0
                        continue
                    }
                    maxNum = maxNum - t.quantity
                }
            }
            // 如果一次没添加，则表示操作的第一个
            if (countNum == 0) {
                maxNum = row.maxQuantity
            }
            if (row.quantity >= maxNum) {
                row.quantity = this.fixed4(maxNum)
            } else {
                row.quantity = this.fixed4(row.quantity)
            }
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 返回
        handleClose () {
            this.$router.push({
                path: '/supplierSys/sheet/sheet',
            })
        },
        onChangeTab (e) {
            const height = $('#' + e.name).offset().top
            try {
                $('#tabs-content').scrollTo(height - this.topHeight, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        beforeDestroy () {
            window.removeEventListener('scroll', this.winEvent.onScroll)
            window.removeEventListener('resize', this.winEvent.onResize)
        }
    },
    mounted () {
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        const arr = ['baseInfo', 'reconciliationDtl']
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({ name: this.$route.query.name })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    }
}
</script>

<style lang='scss' scoped>
.warningTabs {
    padding-top: 70px;
}
.e-table {
    min-height: auto;
    background: #fff;
}

/deep/ .e-form .el-form-item {
    .el-form-item__content {
        height: unset !important;
        display: flex;
        align-items: center;
    }
    .el-form-item__label {
        min-width: 200px;
    }
}
.action span {
    color: #216EC6;
    cursor: pointer;
    user-select: none;

    &:not(:last-of-type) {
        margin-right: 15px;
    }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

/deep/ .el-dialog {
    .el-dialog__body {
        height: 500px;
        margin-top: 0px;
    }
}

</style>