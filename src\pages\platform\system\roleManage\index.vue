<template>
  <div class="base-page">
    <!-- 列表 -->
    <div class="right">
      <div class="e-table">
        <!-- -搜索栏----------------------------搜索栏 -->
        <div class="top">
          <div>
            <div class="left">
              <div class="left-btn">
                  <el-button type="primary" @click="operate_td('新增')" class="btn-greenYellow">新增</el-button>
                  <!-- <el-button type="primary" @click="handleDelete" class="btn-delete">批量删除</el-button> -->
              </div>
            </div>
          </div>
          <!-- 新增按钮 -->
          <div class="search_box">
            <div>机构类型</div>
            <el-select style="margin: 0 10px;width: 160px;" @change="onSearch" v-model="jgValue" placeholder="请选择机构类型">
                <el-option v-for="item in jgOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </el-select>
            <el-input style="width: 300px;" type="text" @keyup.enter.native="onSearch" placeholder="输入搜索关键字" v-model="keywords"><img src="@/assets/search.png" slot="suffix" @click="onSearch" /></el-input>
          </div>
        </div>
        <!-- -搜索栏----------------------------搜索栏 -->
      </div>
      <!-- ---------------------------------表格开始--------------------------------- -->
      <div class="e-table" :style="{ width: '100%' }">
        <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
            @current-change="handleCurrentChange" row-key="systemId">
          <el-table-column label="操作" width="250">
            <template slot-scope="scope">
                <span class="action" @click="operate_td('编辑', scope.row)">编辑</span>
                <span class="action" @click="operate_td('删除', scope.row)">删除</span>
            </template>
          </el-table-column>
          <el-table-column label="角色名称" width="" prop="roleName"/>
          <el-table-column label="机构类型" width="" prop="jgType"/>
          <el-table-column label="访问权限" width="" prop="rolePermissionName"/>
          <el-table-column label="创建时间" width="" prop="cjsj"/>
        </el-table>
      </div>
      <!-- 分页器 -->
      <ComPagination :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                     :currentPage.sync="pages.currPage"
                     @currentChange="currentChange" @sizeChange="sizeChange" />
    </div>
    <el-dialog v-dialogDrag :title="roleModal.title" :visible.sync="roleModal.visible" width="40%" :close-on-click-modal="false">
      <!-- ---------------------新增编辑窗口--------------------- -->
      <div class="e-form" style="padding: 0 10px 10px;">
        <el-form :rules="formRules" ref="formEdit" :model="formData" label-width="150px">
          <el-row>
            <el-col :span="24">
                <el-form-item label="机构类型：" prop="jgType">
                    <el-select v-model="formData.jgType" placeholder="请选择机构类型">
                        <el-option v-for="item in jgOptions" :key="item.value" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="角色名称：" prop="roleName">
                <el-input v-model="formData.roleName" placeholder="请输入角色名称"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="访问权限：" prop="rolePermission">
                <el-tree ref="elTree" :data="rolePermissionData" show-checkbox node-key="id"
                    :default-expand-all="true" :props="defaultProps">
                </el-tree>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
                <el-form-item label="备注：" prop="bz">
                    <el-input type="textarea" v-model="formData.bz" placeholder=""></el-input>
                </el-form-item>
            </el-col>
          </el-row>
          <div class="buttons">
            <el-button native-type="button" type="primary" @click="onSave()">保存</el-button>
            <el-button @click="roleModal.visible = false">取消</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
// eslint-disable-next-line no-unused-vars
import { getListSys, update, createSysParam, batchDelete, del } from '@/api/platform/system/systemParam'
// eslint-disable-next-line no-unused-vars
import { debounce, advanceSearch, showLoading, hideLoading, getUuid, selectDataClient, openAppClient, parseList, treeToList } from '@/utils/common'
export default {
    components: {
        ComPagination
    },
    watch: {},
    computed: {
    // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291
        },
    },
    data () {
        return {
            keywords: '', jgValue: undefined, jgOptions: [],
            currentRow: null,
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            tableData: [],
            screenWidth: 0,
            screenHeight: 0,
            requestParams: {},
            roleModal: {
                visible: false, title: '',
            },
            typeOptions: [],
            formData: {
                jgType: undefined,
                roleName: '',
                rolePermission: [],
                bz: '',
            },
            rolePermissionData: [],
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            formRules: {
                jgType: [{ required: true, message: '请选择机构类型', trigger: 'change' }],
                roleName: [
                    { required: true, message: '请输入角色名称', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' },
                    { max: 32, message: '名称长度不能超过32个字符', trigger: 'blur' }
                ],
                bz: [{ max: 128, message: '说明长度不能超过128个字符', validator: this.publicFunc.formFunc.verifySpaces, trigger: 'blur' }]
            },
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    created () {
        let res = [
            { id: '1', label: '后台管理平台', children: [
                { id: '1_1', label: '系统管理', children: [
                    { id: '1_1_1', label: '用户管理' },
                    { id: '1_1_2', label: '用户管理新增' },
                    { id: '1_1_3', label: '角色管理' },
                ] },
                { id: '1_2', label: '商品管理', children: [
                    { id: '1_2_1', label: '商品分类管理' }
                ] }
            ] },
            { id: '2', label: '供应商履约后台', children: [
                { id: '2_1', label: '系统管理', children: [
                    { id: '2_1_1', label: '用户管理' },
                    { id: '2_1_2', label: '用户管理新增' },
                    { id: '2_1_3', label: '角色管理' },
                ] },
                { id: '2_2', label: '商品管理', children: [
                    { id: '2_2_1', label: '商品分类管理' }
                ] }
            ] },
            { id: '3', label: '采购人履约平台', children: [
                { id: '3_1', label: '系统管理', children: [
                    { id: '3_1_1', label: '用户管理' },
                    { id: '3_1_2', label: '用户管理新增' }
                ] },
                { id: '3_2', label: '商品管理', children: [
                    { id: '3_2_1', label: '商品分类管理' }
                ] }
            ] },
        ]
        this.rolePermissionData = res

        let res2 = [
            { value: '1', label: '机构类型1' },
            { value: '2', label: '机构类型2' }
        ]
        this.jgOptions = res2
        this.getTableData()
    },
    methods: {
        // 删除、编辑、新增
        operate_td (title, row ) {
            if(title === '删除') {
                this.clientPop('info', '您确定要删除该信息吗？', async () => {
                    showLoading()
                    del({ id: row.systemId }).then(res => {
                        if(res.message === '操作成功') {
                            /*this.clientPop('suc', '删除成功', () => {
                                this.onSearch()
                            })*/
                            this.$message.success('删除成功')
                            this.onSearch()
                        }else{
                            this.clientPop('warn', res.message, () => {})
                        }
                        hideLoading()
                    })
                    hideLoading()
                })
            }else if (title == '编辑') {
                this.roleModal.title = '编辑角色'
                this.roleModal.visible = true
                this.formData.roleName = row.roleName
                this.formData.jgType = row.jgType
                this.formData.bz = row.bz
                let rolePermission = row.rolePermission ? row.rolePermission.split(',') : []
                this.$nextTick(() => {
                    this.$refs.elTree.setCheckedKeys(rolePermission)
                })
                this.$refs.formEdit.clearValidate()
            }else if (title == '新增') {
                this.roleModal.title = '新增角色'
                this.roleModal.visible = true
                this.formData.roleName = ''
                this.formData.jgType = undefined
                this.formData.bz = ''
                this.$nextTick(() => {
                    this.$refs.elTree.setCheckedKeys([])
                })
                this.$refs.formEdit.clearValidate()
            }
        },
        // 批量删除
        handleDelete () {
            if(!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要删除的信息', () => {})
            }
            this.clientPop('info', '您确定要删除选中信息吗？', async () => {
                showLoading()
                let arr = this.selectedRows.map(item => {
                    return item.systemId
                })
                batchDelete(arr).then(res => {
                    if(res.message === '操作成功') {
                        /* this.clientPop('suc', '删除成功', () => {
                            this.onSearch()
                        })*/
                        this.$message.success('删除成功')
                        this.onSearch()
                    }
                })
                hideLoading()
            })
            hideLoading()
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        // 获取列表数据
        async getTableData () {
            this.requestParams = {
                keywords: this.keywords,
                jgValue: this.jgValue,
                limit: this.pages.pageSize
            }
            this.requestParams.page = this.pages.currPage
            getListSys(this.requestParams).then(res => {
                res.list = [
                    { roleName: '总经理1', jgType: '股份', rolePermission: '1_1_1,1_1_2,1_1', rolePermissionName: '本级及下机构', cjsj: '2019-05-07 10:05:05' },
                    { roleName: '总经理2', jgType: '股份', rolePermission: '1_1_1', rolePermissionName: '本级及下机构', cjsj: '2019-05-07 10:05:05' },
                    { roleName: '总经理3', jgType: '股份', rolePermission: '1_1_2', rolePermissionName: '本级及下机构', cjsj: '2019-05-07 10:05:05' }
                ]
                this.tableData = res.list
                this.pages = res
            })
        },
        // 关键词搜索
        onSearch () {
            this.pages.currPage = 1
            this.getTableData()
        },
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 保存新增/修改
        onSave () {
            let checkKey = this.$refs.elTree.getCheckedKeys()
            console.log(checkKey)
            this.$refs.formEdit.validate(valid => {
                if (valid) {
                    this.clientPop('info', '确认保存数据吗？', () => {
                        if (this.roleModal.title === '编辑角色') {
                            return this.handleEditData()
                        }
                        this.handleCreateData()
                    })
                }
            })
        },
        // 修改数据
        handleEditData () {
            update(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.roleModal.visible = false
                }
            })
        },
        // 保存数据
        handleCreateData () {
            createSysParam(this.formData).then(res => {
                if (res.message == '操作成功') {
                    this.$message.success('保存成功')
                    this.getTableData()
                    this.roleModal.visible = false
                }
            })
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.right .top {padding-right: 10px}
.base-page .left {
  min-width: 180px;
  padding: 0;
}

.base-page {
  width: 100%;
  height: 100%;
}
/deep/ .el-dialog .el-dialog__body {margin: 0;height: auto;}
/deep/ .e-form .buttons {position: relative;background: transparent;}
/deep/ .e-form .el-form-item .el-form-item__explain {
  display: flex;
  align-items: center;
}

.detail-info {
  width: 100%;
  // min-width: 670px;
  padding: 30px 20px 70px;
  background-color: #fff;
  border-radius: 14px;
  box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
  position: relative;

  .buttons {
    right: 30px;
    bottom: 20px;
    position: absolute;
    text-align: right;
  }
}
/deep/ .e-form .el-form-item {
    align-items: start;
}

.e-table {
  min-height: auto;
}
.separ {
  display: inline-block;
  font-weight: bold;
  width: 4%;
  margin: 0 1% 0 1%;
  text-align: center;
}

/deep/.el-input--suffix .el-input__inner {
  padding-right: 5px;
  border-radius: 5px;
}

.action {
  margin-right: 10px;
  color: #2e61d7;
  cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
  // height: 150px !important; // 单独修改表格行高度
}

</style>
