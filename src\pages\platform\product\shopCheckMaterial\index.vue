<template>    <!-- 后台管理平台 管理员审核自营店零星采购上架商品 列表 -->
    <div class="base-page">
        <div class="left" style="height: 80vh;">
            <select-material-class ref="materialClassRef" :productType="0"/>
        </div>
        <!-- 列表 -->
        <div class="right">
            <div class="e-table">
                <div class="top">
                    <div class="left">
                        <el-button class="btn-greenYellow" @click="changeSortValue">批量修改排序值</el-button>
                        <el-button class="btn-greenYellow" @click="updateBatchState(1,'批量通过')">批量通过</el-button>
                        <el-button class="btn-delete" @click="updateBatchState(4,'批量不通过')">批量不通过</el-button>
                    </div>
                    <el-dropdown @command="handleChangeSort" trigger="click" placement="bottom">
                        <span class="pointer">
                            排序方式<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item :command="1" :style="{ color: init.orderBy === 1 ? '#2e61d7' : '' }">
                                排序值
                            </el-dropdown-item>
                            <el-dropdown-item :command="2" :style="{ color: init.orderBy === 2 ? '#2e61d7' : '' }">
                                创建时间
                            </el-dropdown-item>
                            <el-dropdown-item :command="3" :style="{ color: init.orderBy === 3 ? '#2e61d7' : '' }">
                                修改时间
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <div class="search_box" style="width: 400px">
                        <el-input
                            clearable
                            type="text"
                            @blur="handleInputSearch"
                            placeholder="输入搜索关键字"
                            v-model="init.keywords"
                        >
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
            </div>
            <!--            表格-->
            <div class="e-table">
                <el-table
                    v-loading="tableLoading" class="table" :height="rightTableHeight" :data="tableData" border
                    @selection-change="selectionChangeHandle" @row-click="handleCurrentInventoryClick" ref="mainTable"
                >
                    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="操作" width="160">
                        <template v-slot="scope">
                            <!-- 固定加成率不需上级审核 -->
                            <div v-if="scope.row.markUp == 0 || scope.row.markUp == 1 || (scope.row.markUp == 2 && scope.row.jcState == 4)">
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.state===3"
                                size="mini"
                                type="success"
                                @click="updateState(scope.row,1,'通过')"
                            >通过
                            </el-button>
                            <el-button
                                style="padding:0 8px;" v-if="scope.row.state===3"
                                size="mini"
                                type="danger"
                                @click="updateState(scope.row,4,'不通过')"
                            >不通过
                            </el-button>
                            </div>
                            <!-- 自定义加成率需要审批 -->
                            <div v-if="scope.row.markUp == 2">
                                <div v-if="scope.row.jcState == 0 && showShenhe">
                                    <el-button
                                        style="padding:0 8px;"
                                        size="mini"
                                        type="success"
                                        @click="updateProductJcState(scope.row,2,'')"
                                    >审核通过
                                    </el-button>
                                    <el-button
                                        style="padding:0 8px;"
                                        size="mini"
                                        type="danger"
                                        @click="updateProductJcState(scope.row,1,'')"
                                    >审核不通过
                                    </el-button>
                                </div>
                                <div v-if="scope.row.jcState == 2 && showShenDing">
                                    <el-button
                                        style="padding:0 8px;"
                                        size="mini"
                                        type="success"
                                        @click="updateProductJcState(scope.row,4,'')"
                                    >审批通过
                                    </el-button>
                                    <el-button
                                        style="padding:0 8px;"
                                        size="mini"
                                        type="danger"
                                        @click="updateProductJcState(scope.row,3,'')"
                                    >审批不通过
                                    </el-button>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="名称" width="200">
                        <template v-slot="scope">
                            <span class="action" @click="handleView(scope.row)">{{ scope.row.relevanceName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="物资分类" width="240" prop="classPath"/>
                    <el-table-column label="物资名称" width="140" prop="relevanceName"/>
                    <el-table-column label="商品编码" width="240" prop="serialNum"/>
                    <el-table-column label="店铺名称" width="160" prop="shopName"/>
                    <el-table-column label="图片" width="120" type="index">
                        <template v-slot="scope">
                            <el-image style="width: 90px; height: 60px" :src="imgUrlPrefixAdd + scope.row.productMinImg"
                                      fit="cover"></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column label="销售价" prop="productMinPrice"></el-table-column>
                    <el-table-column label="税率" prop="taxRate"></el-table-column>
                    <el-table-column label="原价" prop="originalPrice"></el-table-column>
                    <el-table-column label="加成率" width="" prop="markUpNum"></el-table-column>
                    <el-table-column label="排序值" width="120" type="index">
                        <template v-slot="scope">
                            <el-input type="number" clearable v-model="scope.row.sort"
                                      @change="getChangedRow(scope.row)"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="上架时间" width="160" prop="putawayDate"/>
                    <el-table-column label="创建时间" width="160" prop="gmtCreate"/>
                    <el-table-column label="修改时间" width="160" prop="gmtModified"/>
                </el-table>
            </div>
            <!--            分页-->
            <Pagination
                :total="paginationInfo.total"
                :pageSize.sync="paginationInfo.pageSize"
                :currentPage.sync="paginationInfo.currentPage"
                @currentChange="getTableData"
                @sizeChange="getTableData"
            />
        </div>
        <!--高级查询-->
        <el-dialog v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" size="normal">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称：">
                            <el-input clearable maxlength="100" placeholder="请输入商品名称"
                                      v-model="filterData.productName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品编码：">
                            <el-input clearable maxlength="100" placeholder="请输入商品编码"
                                      v-model="filterData.serialNum"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="店铺名称：">
                            <el-input clearable maxlength="100" placeholder="请输入店铺名称"
                                      v-model="filterData.shopName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="价格以上：">
                            <el-input clearable v-model="filterData.abovePrice" placeholder="请输入价格区间"
                                      style="width: 200px"></el-input>
                        </el-form-item>
                        <el-form-item label="价格以下：">
                            <el-input clearable type="number" v-model="filterData.belowPrice"
                                      placeholder="请输入价格区间" style="width: 200px"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.createDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="修改时间：">
                            <el-date-picker
                                value-format="yyyy-MM-dd HH:mm:ss"
                                v-model="filterData.modifiedDate"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!--                <el-row>-->
                <!--                    <el-col :span="24">-->
                <!--                        <el-form-item label="状态：">-->
                <!--                            <div style="display:flex">-->
                <!--                                <el-checkbox v-model="filterData.stateCheckAll" @change="stateAllSelect">全部</el-checkbox>-->
                <!--                                <el-checkbox-group style="margin-left: 30px" v-model="filterData.state"  @change="stateGroupChange">-->
                <!--                                    <el-checkbox :label="filterData.stateOptions[0].value">{{filterData.stateOptions[0].label}}</el-checkbox>-->
                <!--                                    <el-checkbox :label="filterData.stateOptions[1].value">{{filterData.stateOptions[1].label}}</el-checkbox>-->
                <!--                                    <el-checkbox :label="filterData.stateOptions[2].value">{{filterData.stateOptions[2].label}}</el-checkbox>-->
                <!--                                </el-checkbox-group>-->
                <!--                            </div>-->
                <!--                        </el-form-item>-->
                <!--                    </el-col>-->
                <!--                </el-row>-->
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="排序：">
                            <el-radio v-model="init.orderBy" :label="1">按排序值排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="2">按创建时间排序</el-radio>
                            <el-radio v-model="init.orderBy" :label="3">按修改时间排序</el-radio>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible=false">取消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import '@/utils/jquery.scrollTo.min'
import SelectMaterialClass from '@/components/classTree'
import Pagination from '@/components/pagination/pagination'
import { debounce } from '@/utils/common'
import { mapState } from 'vuex'
import {
    allProductStatePass,
    listMaterialPage,
    updateBatch,
    updateProductState,
    updateProductJcState,
} from '@/api/platform/product/materialManage'
import { getProcessConfigDtlById } from '@/api/platform/process/processConfig'

export default {
    components: {
        SelectMaterialClass, Pagination
    },
    watch: {
        'init.orderBy': {
            handler () {
                this.getTableData()
            }
        }
    },
    computed: {
        ...mapState(['userInfo']),
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 292
        },
        // 分配用户、分配岗位列表宽度
        rightTableWidth2 () {
            return (this.screenWidth - 300) + 'px'
        },
        rightTableHeight2 () {
            return this.screenHeight - 210
        }
    },
    data () {
        return {
            showWaringRecordList: false,
            productWaringList: [],
            showWaringRecord: false,
            showWaringRecordLoading: false,
            productWaring: [],
            currentQuery: null,
            tableLoading: false,
            // 表格数据
            changedRow: [], // 排序批量修改
            init: {
                state: [3],
                productType: 0,
                orderBy: 3,
                classId: null,
                keywords: null,
                classPath: [],
            },
            // 商品库
            showDeviceDialog: false,
            inventory: {
                selectRow: [],
                tableData: [],
                keywords: null,
                paginationInfo: { // 分页
                    total: 200,
                    pageSize: 20,
                    currentPage: 1,
                },
            },
            dataListSelections: [], //表格选中的数据
            waringDataListSelections: [], //表格选中的数据
            queryVisible: false, // 高级搜索显示
            paginationInfo: { // 分页
                total: 200,
                pageSize: 20,
                currentPage: 1,
            },
            tableData: [], // 表格数据
            // 高级搜索
            filterData: {
                shopName: null,
                serialNum: null,
                productName: null,
                stateCheckAll: false, // 选择全局
                state: [], // 状态
                belowPrice: null,
                abovePrice: null,
                putawayDate: [], // 上架时间
                createDate: [], // 创建时间
                modifiedDate: [], // 修改时间
                stateOptions: [{
                    value: 0,
                    label: '待上架'
                }, {
                    value: 1,
                    label: '已上架'
                }, {
                    value: 2,
                    label: '已下架'
                }],
            },
            // 高和宽
            screenWidth: 0,
            screenHeight: 0,
            showShenhe: false,
            showShenDing: false,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    },
    // keepAlive使用了触发
    activated () {
        this.getTableData()
    },
    created () {
        this.orgId = this.userInfo.orgInfo.orgId
        // this.getTableData()
        this.getProcessConfigDtlById()
    },
    methods: {
        getProcessConfigDtlById () {
            getProcessConfigDtlById({ processId: 6 }).then(res => {
                if (res != null) {
                    res.processConfigDtlItemVOs.filter(e=>{
                        if (e.roleName == '电商运营部负责人' && e.userId == this.userInfo.userId) {
                            this.showShenhe = true
                        }
                        if (e.roleName == '分管领导' && e.userId == this.userInfo.userId) {
                            this.showShenDing = true
                        }
                    })
                }
            })
        },
        updateProductJcState (row, state, yj) {
            let params = {
                productId: row.productId,
                jcState: state
            }
            if (state == 1 || state == 2) {
                params.jcFzrYj = yj
            }
            if (state == 3 || state == 4) {
                params.jcLdYj = yj
            }
            if (state == 4) {
                updateProductState({ productIds: [row.productId], state: 1 }).then(res => {
                    if (res.code === 200) {
                        this.$message.success(res.message)
                        this.getTableData()
                    }
                })
            }
            updateProductJcState(params).then(res => {
                if (res.code === 200) {
                    this.getTableData()
                }
            })
        },
        selectionChangeHandleWaring (val) {
            this.waringDataListSelections = val
        },
        auditWaring () {
            let ids = this.waringDataListSelections.map(item => {
                return item.productId
            })
            if (ids.length > 0) {
                let params = {
                    productIds: ids,
                    state: 1
                }
                this.tableLoading = true
                updateProductState(params).then(res => {
                    if (res.code === 200) {
                        this.$message.success(res.message)
                        this.dataListSelections = []
                        this.getTableData()
                    }
                }).finally(() => this.tableLoading = false,
                    this.showWaringRecord = false,
                    this.showWaringRecordLoading = false)
            }else {
                this.$message.error('请勾选审核数据')
            }

        },
        handleViewDtl (row) {
            this.productWaringList = row.auditProductList
            this.showWaringRecordList = true
        },
        updateBatchState (state, title) {
            if (this.dataListSelections.length === 0) {
                this.$confirm('检测到未选中数据，该操作是全部操作您是否确认?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.tableLoading = true
                    allProductStatePass(this.currentQuery).then(res => {
                        if (res.code === 200) {
                            this.$message.success(res.message)
                            this.getTableData()
                        }
                    }).finally(() => this.tableLoading = false)
                })
                return
            }
            let ids = this.dataListSelections.map(item => {
                return item.productId
            })
            var message = '您好！您'
            this.dataListSelections.forEach(row => {
                if (row.productMinPrice > row.avePriceUpLimit) {
                    message = message + '设置的“' + row.productName + '”商品销售价格设置过高，该商品均价为：' + row.productAvePrice + '元。'
                } else if (row.productMinPrice < row.avePriceLowerLimit) {
                    message = message + '设置的“' + row.productName + '”商品销售价格设置过低，该商品均价为：' + row.productAvePrice + '元。'
                } else {
                    message = '您确定要批量【' + title + '】操作吗？'
                }
            })
            this.clientPop('info', message, async () => {
                if (state === 4) {
                    this.$prompt('拒绝原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入拒绝原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入拒绝原因'
                    }).then(({ value }) => {
                        let params = {
                            productIds: ids,
                            state: state,
                            failReason: value,
                        }
                        this.tableLoading = true
                        updateProductState(params).then(res => {
                            if (res.code === 200) {
                                this.$message.success(res.message)
                                this.dataListSelections = []
                                this.getTableData()
                            }
                        })
                    }).finally(() => this.tableLoading = false)
                }
                if (state === 1) {
                    let params = {
                        productIds: ids,
                        state: state
                    }
                    this.tableLoading = true
                    updateProductState(params).then(res => {
                        if (res.code === 200) {
                            this.$message.success(res.message)
                            this.dataListSelections = []
                            this.getTableData()
                        }
                    }).finally(() => this.tableLoading = false)
                }
            })
        },
        handleChangeSort (value) {
            this.init.orderBy = value
        },
        // 开始导入
        inventoryImportBatch () {
            if (this.inventory.selectRow.length === 0) {
                return this.$message('请勾选要导入的数据！')
            }
        },
        // 点击选中
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.mainTable.toggleRowSelection(row, row.flag)
        },
        // 选择
        selectDeviceRow (value) {
            this.inventory.selectRow = value
        },
        // 批量修改排序
        changeSortValue () {
            if (this.changedRow.length === 0) {
                return this.$message('未修改列表当中的排序值！')
            }
            this.clientPop('info', '您确定要批量修改这些数据吗！', async () => {
                this.tableLoading = true
                updateBatch(this.changedRow).then(res => {
                    this.getTableData()
                    this.changedRow = []
                    this.tableLoading = false
                    this.message(res)
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        // 排序变换行
        getChangedRow (row) {
            if (row.sort <= 0) {
                row.sort = 0
            }
            if (this.changedRow.length === 0) {
                this.changedRow.push({ productId: row.productId, sort: row.sort })
                return
            }
            let flag = false
            this.changedRow.forEach(t => {
                if (t.productId === row.productId) {
                    t.sort = row.sort
                    flag = true
                }
            })
            if (!flag) {
                this.changedRow.push({ productId: row.productId, sort: row.sort })
            }
            flag = true
        },
        skipView (data) {
            this.$router.push({
                //path后面跟跳转的路由地址
                path: '/platform/product/shopCheckMaterialDetail',
                //name后面跟跳转的路由名字（必须有亲测，不使用命名路由会传参失败）
                name: 'shopCheckMaterialDetail',
                params: {
                    row: data
                }
            })
        },
        //新增
        add () {
            let data = {}
            data.viewType = 'add'
            data.classPath = this.classPath
            this.skipView(data)
        },
        // 物资详情
        handleView (row) {
            row.classPath = this.classPath
            this.skipView(row)
        },
        resetSearchConditions () {
            this.filterData.shopName = ''
            this.filterData.serialNum = ''
            this.filterData.productName = ''
            this.filterData.putawayDate = []
            this.filterData.createDate = []
            this.filterData.modifiedDate = []
            this.filterData.belowPrice = ''
            this.filterData.abovePrice = ''
            this.filterData.state = []
            this.filterData.stateCheckAll = false
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getTableData()
        },
        // 高级搜索确认
        confirmSearch () {
            this.init.keywords = ''
            this.getTableData()
            this.queryVisible = false
        },
        // 状态分组变化
        stateGroupChange (value) {
            this.filterData.stateCheckAll = value.length === this.filterData.stateOptions.length
        },
        // 状态全选
        stateAllSelect (value) {
            if (value) {
                this.filterData.state = this.filterData.stateOptions.map(t => t.value)
            } else {
                this.filterData.state = []
            }
        },
        // 表格选中事件
        selectionChangeHandle (val) {
            this.dataListSelections = val
        },
        // 单个上架
        updateState (row, state, title) {
            var message = ''
            if (row.productMinPrice > row.avePriceUpLimit) {
                message = '您好！您设置的“' + row.productName + '”商品销售价格设置过高，该商品均价为：' + row.productAvePrice + '元。'
            } else if (row.productMinPrice < row.avePriceLowerLimit) {
                message = '您好！您设置的“' + row.productName + '”商品销售价格设置过低，该商品均价为：' + row.productAvePrice + '元。'
            } else {
                message = '您确定要对“' + row.productName + '”进行【' + title + '】操作吗！'
            }
            this.clientPop('info', message, async () => {
                if (state === 4) {
                    this.$prompt('不通过原因', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'error',
                        inputType: 'textarea',
                        inputPlaceholder: '请输入不通过原因',
                        inputPattern: /^.+$/,
                        inputErrorMessage: '请输入不通过原因'
                    }).then(({ value }) => {
                        let params = {
                            productIds: [row.productId],
                            state: state,
                            failReason: value,
                        }
                        this.tableLoading = true
                        updateProductState(params).then(res => {
                            if (res.code === 200) {
                                this.$message.success(res.message)
                                this.getTableData()
                            }
                        }).finally(() => this.tableLoading = false)
                    })
                } else {
                    let params = {
                        productIds: [row.productId],
                        state: state
                    }
                    this.tableLoading = true
                    updateProductState(params).then(res => {
                        if (res.code === 200) {
                            this.$message.success(res.message)
                            this.getTableData()
                        }
                    }).finally(() => {
                        this.tableLoading = false
                    })

                }
            })
        },
        // 批量修改上下架状态
        updateStateBatch (state) {
            if (this.dataListSelections.length === 0) {
                return this.$message('请勾选要修改的数据！')
            }
            let params = {
                productIds: this.dataListSelections.map(item => {
                    return item.productId
                }),
                state: state
            }
            this.clientPop('info', '您确定要批量上下/下架这些物资吗！', async () => {
                this.tableLoading = true
                updateProductState(params).then(res => {
                    this.getTableData()
                    this.dataListSelections = []
                    this.message(res)
                }).finally(() => {
                    this.tableLoading = false
                })
            })
        },
        // 分类点击
        classNodeClick (data, nodePath) {
            this.init.classId = data.classId
            this.classPath = nodePath
            this.getTableData()
        },
        // 获取表格数据
        getTableData () {
            let params = {
                state: this.init.state,
                shopId: this.init.shopId,
                //productType: this.init.productType,
                page: this.paginationInfo.currentPage,
                limit: this.paginationInfo.pageSize,
            }
            if (this.filterData.productName != null) {
                params.productName = this.filterData.productName
            }
            if (this.filterData.shopName != null) {
                params.shopName = this.filterData.shopName
            }
            if (this.filterData.serialNum != null) {
                params.serialNum = this.filterData.serialNum
            }
            if (this.filterData.state.length !== 0) {
                params.state = this.filterData.state
            }
            if (this.init.classId != null) {
                params.classId = this.init.classId
            }
            if (this.init.keywords != null) {

                params.keywords = this.init.keywords
            }
            if (this.filterData.modifiedDate != null) {
                params.startModifiedDate = this.filterData.modifiedDate[0]
                params.endModifiedDate = this.filterData.modifiedDate[1]
            }
            if (this.filterData.createDate != null) {
                params.startCreateDate = this.filterData.createDate[0]
                params.endCreateDate = this.filterData.createDate[1]
            }
            // if(this.filterData.putawayDate.length != 0) {
            //     params.startPutawayDate = this.filterData.putawayDate[0],
            //     params.endPutawayDate = this.filterData.putawayDate[1]
            // }
            if (this.init.orderBy != null) {
                params.orderBy = this.init.orderBy
            }
            if (this.filterData.belowPrice != null) {
                params.belowPrice = this.filterData.belowPrice
            }
            if (this.filterData.abovePrice != null) {
                params.abovePrice = this.filterData.abovePrice
            }
            this.tableLoading = true
            this.currentQuery = params
            listMaterialPage(params).then(res => {
                this.tableData = res.list
                this.paginationInfo.total = res.totalCount
                this.paginationInfo.pageSize = res.pageSize
                this.paginationInfo.currentPage = res.currPage
            }).finally(() => {
                this.tableLoading = false
            })
        },
        // 消息提示
        message (res) {
            if (res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
        getScreenInfo () {
            this.screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight
        }
    }
}
</script>

<style lang="scss" scoped>
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type=‘number’] {
    -moz-appearance: textfield !important;
}

.el-dialog__body {
    margin: 220px;
}

.base-page .left {
    min-width: 200px;
    height: 100%;
    padding: 0;
    overflow-y: scroll;
    flex-wrap: wrap;

    &::-webkit-scrollbar {
        width: 0;
        height: 0;
    }
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .el-dropdown {
    min-width: 75px;
    margin-right: 20px;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.e-table {
    min-height: auto;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

//弹窗
/deep/ .el-dialog {
    .el-dialog__body {
        height: 400px;
        margin-top: 0;
    }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ input[type='number'] {
    -moz-appearance: textfield !important;
}

/deep/ #supplierDialog {
    .el-dialog__body {
        height: 580px;
        margin-top: 0;
    }
}
</style>
