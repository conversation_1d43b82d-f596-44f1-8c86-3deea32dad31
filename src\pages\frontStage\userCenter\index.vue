<template>
    <div class="root">
        <div class="main df">
            <div class="main_left main_leftDiv">
                <!-- <div class="userMenu"> -->
                <el-menu :default-active="activeItem" :unique-opened="true" class="el-menu-vertical-user"
                         :router="true">
                    <!-- 首页 -->
                    <el-menu-item class="userCenter_personal_center" :route="{ path: '/user' }" index="1">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>个人中心</span>
                        </template>
                    </el-menu-item>
                    <el-submenu class="userCenter_planList" index="2" v-show="showPlan">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>计划列表</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item v-show="showPlan" class="userCenter_plan" :route="{ path: '/user/plan/0' }" index="2-1">
<!--                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>零星采购计划</span>
                                </template>-->
                              <template slot="title">
                                <div class="dot"></div>
                                零星采购计划
                              </template>
                            </el-menu-item>
                            <el-menu-item v-show="showPlan" class="userCenter_plan" :route="{ path: '/user/plan/1' }" index="2-2">
<!--                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>大宗临购计划</span>
                                </template>-->
                              <template slot="title">
                                <div class="dot"></div>
                                大宗临购计划
                              </template>
                            </el-menu-item>
                            <el-menu-item v-show="showPlan" class="userCenter_plan" :route="{ path: '/user/plan/2' }" index="2-3">
<!--                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>周转材料计划</span>
                                </template>-->
                              <template slot="title">
                                <div class="dot"></div>
                                周转材料计划
                              </template>
                            </el-menu-item>
<!--                            <el-menu-item v-show="showDevFunc" class="userCenter_plan" :route="{ path: '/user/monthPlan' }" index="2-1">-->
<!--                                <template slot="title">-->
<!--                                    <img class="icon" src="" alt="">-->
<!--                                    <span>大宗月供计划</span>-->
<!--                                </template>-->
<!--                            </el-menu-item>-->
                            <el-menu-item class="userCenter_plan" :route="{ path: '/user/synthesizeTemporary' }" index="2-4">
<!--                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>大宗临购清单</span>
                                </template>-->
                              <template slot="title">
                                <div class="dot"></div>
                                大宗临购清单
                              </template>
                            </el-menu-item>
                            <!-- <el-menu-item  class="userCenter_plan" :route="{ path: '/user/synthesizeTemporaryPlan' }" index="4-1">
                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>大宗临购计划</span>
                                </template>
                            </el-menu-item> -->
                            <el-menu-item class="userCenter_plan" :route="{ path: '/user/turnoverMaterials' }" index="5-1">
<!--                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>周转材料清单</span>
                                </template>-->
                              <template slot="title">
                                <div class="dot"></div>
                                周转材料清单
                              </template>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <el-submenu class="userCenter_order" index="3"  v-show="showPlan">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>订单中心</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item :route="{ path: '/user/order' }" index="3-1">
                                <template slot="title">
                                    <div class="dot"></div>
                                    零星采购订单
                                </template>
                            </el-menu-item>

                            <el-menu-item :route="{ path: '/user/blockOrder' }" index="3-1-3" >
                                <template slot="title">
                                    <div class="dot"></div>
                                    大宗临购订单
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/zzOrder' }" index="3-1-4" >
                                <template slot="title">
                                    <div class="dot"></div>
                                    周转材料订单
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/orderReview' }" index="3-2">
                                <template slot="title">
                                    <div class="dot"></div>
                                    零星采购订单评价
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/synthesisMaterialReview' }" index="3-2-1">
                                <template slot="title">
                                    <div class="dot"></div>
                                    大宗临购订单评价
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/circulatingMaterialReview' }" index="3-2-2">
                                <template slot="title">
                                    <div class="dot"></div>
                                    周转材料订单评价
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/refund' }" index="3-3">
                                <template slot="title">
                                    <div class="dot"></div>
                                    我的退货
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/myInvoice' }" index="3-4" v-if="showDevFunc">
                                <template slot="title">
                                    <div class="dot"></div>
                                    我的发票
                                </template>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu>
                    <el-submenu class="userCenter_like" index="4">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>我的收藏</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item :route="{ path: '/user/followingProduct' }" index="4-1">
                                <template slot="title">
                                    <div class="dot"></div>
                                    商品关注
                                </template>
                            </el-menu-item>
                            <!-- <el-menu-item :route="{ path: '' }" index="4-2">
                                    <template slot="title">
                                        <div class="dot"></div> 店铺关注
                                    </template>
                                </el-menu-item> -->
                        </el-menu-item-group>
                    </el-submenu>
                    <!-- 我的发票 -->
                    <!--                  <el-menu-item class="userCenter_invoice" :route="{ path: '/user/myInvoice' }" index="5">-->
                    <!--                        <template slot="title">-->
                    <!--                            <img class="icon" src="" alt="">-->
                    <!--                            <span>我的发票</span>-->
                    <!--                        </template>-->
                    <!--                    </el-menu-item>-->
                    <!-- 我的购物车 -->
<!--                    <el-menu-item class="userCenter_cart" :route="{ path: '/user/userCart' }" index="6">-->
<!--                        <template slot="title">-->
<!--                            <img class="icon" src="" alt="">-->
<!--                            <span>我的购物车</span>-->
<!--                        </template>-->
<!--                    </el-menu-item>-->
                    <!-- 安全设置 -->
                    <!-- <el-submenu class="userCenter_security" index="7" v-show="!showPlan">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>安全设置</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item :route="{ path: '/user/changePass' }" index="7-1">
                                <template slot="title">
                                    <div class="dot"></div>
                                    修改登录密码
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/changePhone' }" index="7-2">
                                <template slot="title">
                                    <div class="dot"></div>
                                    修改手机号
                                </template>
                            </el-menu-item>
                        </el-menu-item-group>
                    </el-submenu> -->
                    <!-- 个人资料 -->
                    <el-submenu class="userCenter_userInfo" index="8">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>个人资料</span>
                        </template>
                        <el-menu-item-group>
                            <el-menu-item :route="{ path: '/user/shippingAddr' }" index="8-1">
                                <template slot="title">
                                    <div class="dot"></div>
                                    收货地址
                                </template>
                            </el-menu-item>
                            <el-menu-item :route="{ path: '/user/changeAvatar' }" index="8-2">
                                <template slot="title">
                                    <div class="dot"></div>
                                    修改头像、昵称
                                </template>
                            </el-menu-item>
                            <div v-show="!showPlan">
                                <el-menu-item :route="{ path: '/user/changePass' }" index="8-3">
                                    <template slot="title">
                                        <div class="dot"></div>
                                        修改登录密码
                                    </template>
                                </el-menu-item>
                                <!-- 待修改 -->
                                <el-menu-item :route="{ path: '/user/changePhone' }" index="8-4">
                                    <template slot="title">
                                        <div class="dot"></div>
                                        修改手机号
                                    </template>
                                </el-menu-item>
                            </div>
                        </el-menu-item-group>
                    </el-submenu>
                    <!-- 认证中心 -->
                    <el-menu-item class="userCenter_cart" :route="{ path: '/user/verification' }" index="9"
                                  v-if="!showPlan">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>认证中心</span>
                        </template>
                    </el-menu-item>
                    <el-menu-item class="userCenter_prices" :route="{ path: '/user/productCompare' }" index="14">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>商品比价历史</span>
                        </template>
                    </el-menu-item>
                    <el-menu-item class="userCenter_news" :route="{ path: '/user/mail' }" index="10">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>消息中心</span>
                        </template>
                    </el-menu-item>
                  <el-menu-item  class="userCenter_feedback" :route="{ path: '/user/feedback' }" index="11">
                    <template slot="title">
                      <img class="icon" src="" alt="">
                      <span>反馈中心</span>
                    </template>
                  </el-menu-item>
                  <el-menu-item  class="userCenter_chart" :route="{ path: '/user/chart' }" index="15">
                    <template slot="title">
                      <img class="icon" src="" alt="">
                      <span>统计分析</span>
                    </template>
                  </el-menu-item>
<!--                    <el-menu-item class="userCenter_cart" :route="{  }" @click="openWindow('/mFront/sheet')" index="13">-->
<!--                        <template slot="title">-->
<!--                            <img class="icon" src="" alt="">-->
<!--                            <span>对账单</span>-->
<!--                        </template>-->
<!--                    </el-menu-item>-->
<!--                    <el-menu-item class="userCenter_cart" :route="{ path: '/user/myPurchase' }" index="12">
                        <template slot="title">
                            <img class="icon" src="" alt="">
                            <span>我的采购</span>
                        </template>
                    </el-menu-item>-->
<!--                    <el-menu-item class="userCenter_cart" :route="{ path: '/user/indexBidding' }" index="11">-->
<!--                        <template slot="title">-->
<!--                            <img class="icon" src="" alt="">-->
<!--                            <span>竞价采购</span>-->
<!--                        </template>-->
<!--                    </el-menu-item>-->
                </el-menu>
                <!-- </div> -->
            </div>
            <div class="view">
                <router-view :key="$route.fullPath"></router-view>
            </div>
        </div>
        <publicity></publicity>
    </div>
</template>
<script>
import publicity from '@/pages/frontStage/components/publicity'
import { mapState } from 'vuex'

export default {
    name: 'personalCenter',
    components: { publicity },
    data () {
        return {
            activeItem: '6-1',
            loginData: {},
            iconList: [
                {
                    img: require('../../../assets/images/img/ico_fukuan.png'),
                    name: '待付款',
                    num: 8,
                },
                {
                    img: require('../../../assets/images/img/ico_shouhuo.png'),
                    name: '待收货',
                },
                {
                    img: require('../../../assets/images/img/ico_pingjia.png'),
                    name: '待评价',
                    num: 3,
                },
                {
                    img: require('../../../assets/images/img/ico_shouhou.png'),
                    name: '退还/售后',
                },
                {
                    img: require('../../../assets/images/img/ico_dingdan.png'),
                    name: '全部订单',
                },
            ],
        }
    },
    computed: {
        ...mapState(['userInfo']),
        showPlan () {
            // 是否外部
            let isExternal = this.userInfo.isExternal
            return isExternal != 1
        },
    },
    created () {
        let path = this.$route.path.split('/')[2]
        let menuIndex = {
            'userCenter': '1',
            'order': '3-1',
            'orderReview': '3-2',
            'synthesisMaterialReview': '3-2-1',
            'circulatingMaterialReview': '3-2-2',
            'refund': '3-3',
            'myInvoice': '3-4',
            'followingProduct': '4-1',
            'productCompare': '14',
            'productCompareDetail': '14',
            'userCart': '6',
            //'changePass': '7-1',
            //'changePhone': '7-2',
            'shippingAddr': '8-1',
            'changeAvatar': '8-2',
            'changePass': '8-3',
            'changePhone': '8-4',
            'verification': '9',
            'mail': '10',
            'indexBidding': '11',
            'publishBidding': '11',
            'feedback': '11',
            'myPurchase': '12',
            'sheet': '13',
            'statistics': '15'
        }
        this.activeItem = menuIndex[path]
    },
    mounted () {
    },
    methods: {
        openWindow (url) {
            window.open(url, '_blank')
        },
        // eslint-disable-next-line
        handleOpen (key, keyPath) {
        },
        // eslint-disable-next-line
        handleClose (key, keyPath) {
        },
    },
}
</script>
<style scoped lang="scss">
@import '../../../assets/css/menuStyle.css';

div {
  line-height: 1;
}

.root {
  background-color: #f5f5f5;
}

.main {
  width: 1426px;
  margin: 0 auto;
  padding: 20px 0;
  justify-content: space-between;

  .main_left {
    width: 200px;
    background: #fff;

    i {
      color: #333;
    }
  }

  .view {
    width: 1206px;
    height: 100%;
    background-color: #fff;
  }
}

/deep/ .el-menu-item {
  &.is-active {
    .arrow {
      background: url(../../../assets/images/userCenter/go2.png);
    }
  }
  &.userCenter_personal_center {
    .icon {
      background: url(../../../assets/images/userCenter/personal_center.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/personal_center_w.png);
      }
    }
  }
  &.userCenter_prices {
    .icon {
      background: url(../../../assets/images/userCenter/product_prices.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/product_prices_w.png);
      }
    }
  }
  &.userCenter_plan {
      .icon {
          background: url(../../../assets/images/userCenter/ico1.png);
      }

      &.is-opened {
          .icon {
              background: url(../../../assets/images/userCenter/ico1w.png);
          }
      }
  }
  &.userCenter_cart {
    .icon {
      background: url(../../../assets/images/userCenter/ico4.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/ico4w.png);
      }
    }
  }
  &.userCenter_feedback {
    .icon {
      background: url(../../../assets/images/userCenter/feedback.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/feedback_w.png);
      }
    }
  }
  &.userCenter_news {
    .icon {
      background: url(../../../assets/images/userCenter/news.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/news_w.png);
      }
    }
  }
  &.userCenter_chart {
    .icon {
      background: url(../../../assets/images/userCenter/statistical_analysis.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/statistical_analysis_w.png);
      }
    }
  }
}

/deep/ .el-submenu {
  &.userCenter_order {
    .icon {
      background: url(../../../assets/images/userCenter/ico1.png);
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico1w.png);
      }
    }
  }
  &.userCenter_planList {
    .icon {
      background: url(../../../assets/images/userCenter/plan_list.png) ;
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/plan_list_w.png) ;
      }
    }
  }
  &.userCenter_like {
    .icon {
      background: url(../../../assets/images/userCenter/ico2.png) ;
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico2w.png);
      }
    }
  }

  &.userCenter_security {
    .icon {
      background: url(../../../assets/images/userCenter/ico5.png);
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico5w.png);
      }
    }
  }

  &.userCenter_userInfo {
    .icon {
      background: url(../../../assets/images/userCenter/ico6.png) ;
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico6w.png);
      }
    }
  }

}
.main_leftDiv /deep/ .el-menu-vertical-user.el-menu>*:not(.is-active) {
  &.userCenter_order {
    .icon {
      background: url(../../../assets/images/userCenter/ico1.png);
    }
  }
  &.userCenter_planList {
    .icon {
      background: url(../../../assets/images/userCenter/plan_list.png) ;
    }
  }
  &.userCenter_like {
    .icon {
      background: url(../../../assets/images/userCenter/ico2.png) ;
    }
  }

  &.userCenter_security {
    .icon {
      background: url(../../../assets/images/userCenter/ico5.png);
    }
  }

  &.userCenter_userInfo {
    .icon {
      background: url(../../../assets/images/userCenter/ico6.png) ;
    }
  }
  .el-submenu__title {
    background: #fff !important;
    span {color: #333;}
    i.el-submenu__icon-arrow {color: #909399;background-image: url(~@/assets/images/userCenter/close1.png);}
  }
}
.main_leftDiv /deep/ .el-menu-vertical-user.el-menu>.is-active {
  background-color: #216EC6 !important;
  .el-submenu__title {
    background: #216EC6 !important;
    span {color: #fff;}
    i.el-submenu__icon-arrow {color: #909399;background-image: url(~@/assets/images/userCenter/close2.png);}
  }
  &.userCenter_order {
    .icon {
      background: url(../../../assets/images/userCenter/ico1w.png);
    }
  }
  &.userCenter_planList {
    .icon {
      background: url(../../../assets/images/userCenter/plan_list_w.png) ;
    }
  }
  &.userCenter_like {
    .icon {
      background: url(../../../assets/images/userCenter/ico2w.png);
    }
  }

  &.userCenter_security {
    .icon {
      background: url(../../../assets/images/userCenter/ico5w.png);
    }
  }

  &.userCenter_userInfo {
    .icon {
      background: url(../../../assets/images/userCenter/ico6w.png);
    }
  }
}
.main_leftDiv /deep/ .el-menu-vertical-user.el-menu>.is-active.is-opened {
  background: #fff !important;
  .el-submenu__title {background: #216EC6 !important;}
}
</style>
