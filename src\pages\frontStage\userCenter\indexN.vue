<template>
    <div class="root">
        <div class="main df">
            <div class="main_left main_leftDiv">
                <!-- <div class="userMenu"> -->
                <el-menu :default-active="activeItem" :unique-opened="true" class="el-menu-vertical-user"
                         :router="true">
                    <template v-for="itemI in menuList">
                        <template v-if="!itemI.children || !itemI.children.length">
                            <el-menu-item :key="itemI.index" class="userCenter_personal_center" :route="{ path: itemI.path }" :index="itemI.index">
                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>{{itemI.title}}</span>
                                </template>
                            </el-menu-item>
                        </template>
                        <template v-else>
                            <el-submenu :key="itemI.index" class="userCenter_planList" :index="itemI.index">
                                <template slot="title">
                                    <img class="icon" src="" alt="">
                                    <span>{{itemI.title}}</span>
                                </template>
                                <el-menu-item-group>
                                    <el-menu-item v-for="subItem in itemI.children" :key="subItem.index" class="userCenter_plan" :route="{ path: subItem.path }" :index="subItem.index">
                                        <template slot="title">
                                            <div class="dot"></div>
                                            {{subItem.title}}
                                        </template>
                                    </el-menu-item>
                                </el-menu-item-group>
                            </el-submenu>
                        </template>
                    </template>
                </el-menu>
            </div>
            <div class="view">
                <router-view :key="$route.fullPath"></router-view>
            </div>
        </div>
        <publicity></publicity>
    </div>
</template>
<script>
import publicity from '@/pages/frontStage/components/publicity'
import { mapState } from 'vuex'

export default {
    name: 'personalCenter',
    components: { publicity },
    data () {
        return {
            menuList: [],
            activeItem: '6-1',
            loginData: {},
            iconList: [
                {
                    img: require('../../../assets/images/img/ico_fukuan.png'),
                    name: '待付款',
                    num: 8,
                },
                {
                    img: require('../../../assets/images/img/ico_shouhuo.png'),
                    name: '待收货',
                },
                {
                    img: require('../../../assets/images/img/ico_pingjia.png'),
                    name: '待评价',
                    num: 3,
                },
                {
                    img: require('../../../assets/images/img/ico_shouhou.png'),
                    name: '退还/售后',
                },
                {
                    img: require('../../../assets/images/img/ico_dingdan.png'),
                    name: '全部订单',
                },
            ],
        }
    },
    computed: {
        ...mapState(['userInfo', 'routerObj']),
        showPlan () {
            // 是否外部
            let isExternal = this.userInfo.isExternal
            return isExternal != 1
        },
    },
    created () {
        let path = this.$route.path.split('/')[2]
        let menuIndex = {
            'userCenter': '1',
            'order': '3-1',
            'orderReview': '3-2',
            'synthesisMaterialReview': '3-2-1',
            'circulatingMaterialReview': '3-2-2',
            'refund': '3-3',
            'myInvoice': '3-4',
            'followingProduct': '4-1',
            'productCompare': '14',
            'productCompareDetail': '14',
            'userCart': '6',
            //'changePass': '7-1',
            //'changePhone': '7-2',
            'shippingAddr': '8-1',
            'changeAvatar': '8-2',
            'changePass': '8-3',
            'changePhone': '8-4',
            'verification': '9',
            'mail': '10',
            'indexBidding': '11',
            'publishBidding': '11',
            'feedback': '11',
            'myPurchase': '12',
            'sheet': '13',
            'statistics': '15'
        }
        this.activeItem = menuIndex[path]
    },
    mounted () {
        this.menuList = this.routerObj.frontStageUser
    },
    methods: {
        openWindow (url) {
            window.open(url, '_blank')
        },
        // eslint-disable-next-line
        handleOpen (key, keyPath) {
        },
        // eslint-disable-next-line
        handleClose (key, keyPath) {
        },
    },
}
</script>
<style scoped lang="scss">
@import '../../../assets/css/menuStyle.css';

div {
  line-height: 1;
}

.root {
  background-color: #f5f5f5;
}

.main {
  width: 1426px;
  margin: 0 auto;
  padding: 20px 0;
  justify-content: space-between;

  .main_left {
    width: 200px;
    background: #fff;

    i {
      color: #333;
    }
  }

  .view {
    width: 1206px;
    height: 100%;
    background-color: #fff;
  }
}

/deep/ .el-menu-item {
  &.is-active {
    .arrow {
      background: url(../../../assets/images/userCenter/go2.png);
    }
  }
  &.userCenter_personal_center {
    .icon {
      background: url(../../../assets/images/userCenter/personal_center.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/personal_center_w.png);
      }
    }
  }
  &.userCenter_prices {
    .icon {
      background: url(../../../assets/images/userCenter/product_prices.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/product_prices_w.png);
      }
    }
  }
  &.userCenter_plan {
      .icon {
          background: url(../../../assets/images/userCenter/ico1.png);
      }

      &.is-opened {
          .icon {
              background: url(../../../assets/images/userCenter/ico1w.png);
          }
      }
  }
  &.userCenter_cart {
    .icon {
      background: url(../../../assets/images/userCenter/ico4.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/ico4w.png);
      }
    }
  }
  &.userCenter_feedback {
    .icon {
      background: url(../../../assets/images/userCenter/feedback.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/feedback_w.png);
      }
    }
  }
  &.userCenter_news {
    .icon {
      background: url(../../../assets/images/userCenter/news.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/news_w.png);
      }
    }
  }
  &.userCenter_chart {
    .icon {
      background: url(../../../assets/images/userCenter/statistical_analysis.png);
    }

    &.is-active {
      .icon {
        background: url(../../../assets/images/userCenter/statistical_analysis_w.png);
      }
    }
  }
}

/deep/ .el-submenu {
  &.userCenter_order {
    .icon {
      background: url(../../../assets/images/userCenter/ico1.png);
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico1w.png);
      }
    }
  }
  &.userCenter_planList {
    .icon {
      background: url(../../../assets/images/userCenter/plan_list.png) ;
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/plan_list_w.png) ;
      }
    }
  }
  &.userCenter_like {
    .icon {
      background: url(../../../assets/images/userCenter/ico2.png) ;
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico2w.png);
      }
    }
  }

  &.userCenter_security {
    .icon {
      background: url(../../../assets/images/userCenter/ico5.png);
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico5w.png);
      }
    }
  }

  &.userCenter_userInfo {
    .icon {
      background: url(../../../assets/images/userCenter/ico6.png) ;
    }

    &.is-opened {
      .icon {
        background: url(../../../assets/images/userCenter/ico6w.png);
      }
    }
  }

}
.main_leftDiv /deep/ .el-menu-vertical-user.el-menu>*:not(.is-active) {
  &.userCenter_order {
    .icon {
      background: url(../../../assets/images/userCenter/ico1.png);
    }
  }
  &.userCenter_planList {
    .icon {
      background: url(../../../assets/images/userCenter/plan_list.png) ;
    }
  }
  &.userCenter_like {
    .icon {
      background: url(../../../assets/images/userCenter/ico2.png) ;
    }
  }

  &.userCenter_security {
    .icon {
      background: url(../../../assets/images/userCenter/ico5.png);
    }
  }

  &.userCenter_userInfo {
    .icon {
      background: url(../../../assets/images/userCenter/ico6.png) ;
    }
  }
  .el-submenu__title {
    background: #fff !important;
    span {color: #333;}
    i.el-submenu__icon-arrow {color: #909399;background-image: url(~@/assets/images/userCenter/close1.png);}
  }
}
.main_leftDiv /deep/ .el-menu-vertical-user.el-menu>.is-active {
  background-color: #216EC6 !important;
  .el-submenu__title {
    background: #216EC6 !important;
    span {color: #fff;}
    i.el-submenu__icon-arrow {color: #909399;background-image: url(~@/assets/images/userCenter/close2.png);}
  }
  &.userCenter_order {
    .icon {
      background: url(../../../assets/images/userCenter/ico1w.png);
    }
  }
  &.userCenter_planList {
    .icon {
      background: url(../../../assets/images/userCenter/plan_list_w.png) ;
    }
  }
  &.userCenter_like {
    .icon {
      background: url(../../../assets/images/userCenter/ico2w.png);
    }
  }

  &.userCenter_security {
    .icon {
      background: url(../../../assets/images/userCenter/ico5w.png);
    }
  }

  &.userCenter_userInfo {
    .icon {
      background: url(../../../assets/images/userCenter/ico6w.png);
    }
  }
}
.main_leftDiv /deep/ .el-menu-vertical-user.el-menu>.is-active.is-opened {
  background: #fff !important;
  .el-submenu__title {background: #216EC6 !important;}
}
</style>
