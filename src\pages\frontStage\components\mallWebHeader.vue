<template>
  <div>
    <nav>
      <div class="topBar"  v-loading="shopLoading">
        <div class="content-box center">
          <div class="btns-user search_bar">
            <div class="user-left dfa">
              <div class="dfa" @click="$router.push('/index')" v-if="$route.path != '/index'">
                <img src="@/assets/images/ico_home_white.png" alt="" />
                <span>返回首页</span>
              </div>
              <div
                v-if="!userInfo.userId"
                class="pointer"
                @click="$router.push('/login')"
              >
                【用户登录】
              </div>
            </div>
            <div class="user-right">
              <div class="dfa" v-if="userInfo.isInterior && userInfo.userId">
                <div class="textOverflow1">当前企业：{{ selectedEnterprise }}</div>
                <el-dropdown
                  class="enterprise"
                  @command="dropChange"
                  trigger="click"
                  split-button
                  v-model="selectedEnterprise"
                >
                  <el-dropdown-menu
                    style="width: unset; max-width: unset"
                    slot="dropdown"
                  >
                    <el-dropdown-item
                      style="padding-right: 30px"
                      @click.native="menuOrgClick(item)"
                      :command="item.orgName"
                      v-for="item in dropdownList"
                      :key="item.orgId"
                    >
                      <span class="textOverflow1">{{ item.orgName }}</span>
                      <img
                        v-show="item.orgName == selectedEnterprise"
                        src="@/assets/images/check.png"
                        alt=""
                      />
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <template v-if="userInfo.userId">
                <span
                  class="order-link"
                  @click="$router.push('/user/order')"
                  v-if="userInfo.isInterior === 1"
                  >订单中心</span
                >
                <div class="cart dfa" @click="$router.push('/user/cart')">
                  <span>购物车</span>
                  <img src="@/assets/images/cart.png" alt="" />
                  <div
                    v-loading="cartNumLoading"
                    class="mark"
                    v-show="cartNum != null && cartNum != 0"
                  >
                    <div>{{ cartNum }}</div>
                  </div>
                </div>
                <div class="greetings">
                  <div class="pop">
                    <span>您好！</span><span>{{ userInfo.userName }}</span>
                    <img src="../../../assets/images/ico_xl .png" alt="" />
                    <div class="pop-menu">
                      <div class="popTop">
                        <div class="user df">
                          <img
                            :src="
                              loginData.userImg
                                ? imgUrlPrefixAdd + loginData.userImg
                                : require('@/assets/images/userCenter/default_avatar.png')
                            "
                            alt=""
                          />
                          <div class="info">
                            <div
                              class="nameTitle textOverflow1 pointer"
                              @click="$router.push('/user')"
                            >
                              {{ loginData.userName }}
                            </div>
                            <div class="idTitle textOverflow1">
                              ID: {{ loginData.userNumber }}
                            </div>
                          </div>
                        </div>
                        <div class="btns df">
                          <div class="dfa" @click="goTo('/user')">
                            个人中心 <i class="el-icon-arrow-right"></i>
                          </div>
                          <div
                            class="dfa"
                            v-if="userInfo.zcstate == 0"
                            @click="goTo('/mFront/register')"
                          >
                            平台初审 <i class="el-icon-arrow-right"></i>
                          </div>
                          <div
                            class="dfa"
                            v-if="userInfo.zcstate == 1"
                            @click="goTo('/mFront/openShop')"
                          >
                            申请开店 <i class="el-icon-arrow-right"></i>
                          </div>
                          <div
                            class="dfa"
                            v-if="userInfo.zcstate == 2"
                            @click="goTo('/mFront/contractSigning')"
                          >
                            待缴费 <i class="el-icon-arrow-right"></i>
                          </div>
                          <!-- <div
                            class="dfa"
                            v-if="
                              loginData.shopId == null &&
                              userInfo.enterpriseType != 2 &&
                              userInfo.isShpAuthority === 1
                            "
                            @click="goCreatShop"
                          >
                            我要开店 <i class="el-icon-arrow-right"></i>
                          </div> -->
                          <div
                            class="dfa"
                            v-if="
                              loginData.shopId != null &&
                              loginData.auditStatus == 1 &&
                              loginData.state == 1 &&
                              userInfo.isShpAuthority === 1 && (loginData.isInterior == 1 ||  shuDaoFlag == 1)
                            "
                            @click="goMainShop"
                          >
                            店铺主页 <i class="el-icon-arrow-right"></i>
                          </div>
                            <!-- <div
                                class="dfa"
                                v-if="
                              loginData.shopId != null &&
                              loginData.auditStatus == 1 &&
                              // loginData.state == 1 &&
                              userInfo.isShpAuthority === 1 &&
                              loginData.isInterior == 0 &&
                              currentStep == 3 && shuDaoFlag == 0 && platformYearFeeRecord == null
                            "
                                @click="goAuthShop"
                            >
                                待缴费 <i class="el-icon-arrow-right"></i>
                            </div> -->
                            <div
                                class="dfa"
                                v-if="
                              loginData.shopId != null &&
                              loginData.auditStatus == 1 &&
                              // loginData.state == 1 &&
                              userInfo.isShpAuthority === 1 &&
                              loginData.isInterior == 0 &&
                              currentStep == 3 && shuDaoFlag == 0 && platformYearFeeRecord != null
                            "
                                @click="goAuthShop"
                            >
                                缴费审核失败 <i class="el-icon-arrow-right"></i>
                            </div>
                            <div
                                class="dfa"
                                v-if="
                              loginData.shopId != null &&
                              loginData.auditStatus == 1 &&
                              // loginData.state == 1 &&
                              userInfo.isShpAuthority === 1 &&
                              loginData.isInterior == 0 &&
                              currentStep == 4 && shuDaoFlag == 0
                            "
                                @click="goAuthShop1"
                            >
                                平台复审 <i class="el-icon-arrow-right"></i>
                            </div>
                            <div
                                class="dfa"
                                v-if="
                              loginData.shopId != null &&
                              loginData.auditStatus == 1 &&
                              loginData.state == 1 &&
                              userInfo.isShpAuthority === 1 &&
                              loginData.isInterior == 0 &&
                              currentStep == 5 && shuDaoFlag == 0
                            "
                                @click="goMainShop"
                            >
                                店铺首页 <i class="el-icon-arrow-right"></i>
                            </div>
                          <div
                            v-if="showDevFunc"
                            class="dfa"
                            @click="goTo('/user/feedback')"
                          >
                            反馈中心 <i class="el-icon-arrow-right"></i>
                          </div>
                          <div
                            class="dfa"
                            v-if="
                              loginData.shopId != null &&
                              loginData.auditStatus == 3 &&
                              userInfo.isShpAuthority === 1
                            "
                            @click="
                              $router.push({
                                path: '/user/statusFail',
                                query: { shopId: loginData.shopId },
                              })
                            "
                          >
                            审核未通过 <i class="el-icon-arrow-right"></i>
                          </div>
                          <div
                            class="dfa"
                            @click="goAuthShop"
                            v-if="
                              loginData.shopId != null &&
                              loginData.auditStatus == 2 &&
                              userInfo.isShpAuthority === 1
                            "
                          >
                            审核中<i class="el-icon-arrow-right"></i>
                          </div>
                        </div>
                        <div class="exit" @click="handleLogout">退出</div>
                      </div>
                      <div class="popBottom">
                        <div class="icons df">
                          <i
                            id="arrow-left"
                            class="el-icon-arrow-left pointer"
                          ></i>
                          <div id="iconBox" class="iconBox dfa">
                            <div
                              class="items dfa pointer"
                              @click="goTo('/platform/mail/outBox')"
                              v-if="
                                this.userInfo.isPlatformAdmin === 1 &&
                                userInfo.roles.includes('物资采购平台管理权限')
                              "
                            >
                              <img
                                src="@/assets/images/userCenter/backstage.png"
                                alt=""
                              />
                              <div>后台管理平台</div>
                            </div>
                            <div
                              class="items dfa pointer"
                              @click="goTo('/performance/trial')"
                              v-if="
                                userInfo.roles &&
                                userInfo.roles.includes('物资采购平台履约系统')
                              "
                            >
                              <img
                                src="@/assets/images/userCenter/backstage.png"
                                alt=""
                              />
                              <div>采购人履约平台</div>
                            </div>
                            <!--<div class="items dfa pointer" @click="goTo('/shopManage')" v-if="this.userInfo.isShpAuthority === 1 && this.userInfo.shopId != null && this.userInfo.auditStatus == 1">
                                                            <img src="@/assets/images/userCenter/shop.png" alt="">
                                                            <div>店铺管理平台</div>
                                                        </div>-->
                            <div
                              class="items dfa pointer"
                              @click="goTo('/supplierSys/shopManage/mall')"
                              v-if="this.userInfo.isSupplier === 1"
                            >
                              <img
                                src="@/assets/images/userCenter/supplier.png"
                                alt=""
                              />
                              <div>供应商履约平台</div>
                            </div>
                            <div
                              class="items dfa pointer"
                              @click="goTo('/inspection/biddingManage/openTendering')"
                              v-if="this.userInfo.isCheck === 1"
                            >
                              <img
                                src="@/assets/images/userCenter/supplier.png"
                                alt=""
                              />
                              <div>纪检平台</div>
                            </div>
                          </div>
                          <i
                            id="arrow-right"
                            class="el-icon-arrow-right pointer"
                          ></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bell dfa" @click="$router.push('/user/mail')">
                  <img src="@/assets/images/msg.png" title="消息提醒" alt="" />
                  <div class="mark" v-show="msgList.length > 0">
                    <div>{{ msgList.length }}</div>
                  </div>
                </div>
              </template>
              <div class="dfa" v-else>
                <img
                  style="width: 18px; height: 18px"
                  src="@/assets/images/ico_register.png"
                  alt=""
                />
                <span @click="$router.push('/mFront/register')"
                  >新用户注册</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-dialog
        class="front"
        :visible.sync="dialogVisible"
        :close-on-click-modal="true"
      >
        <div class="dialog-header">
          <div class="dialog-header-top search_bar">
            <div class="dialog-title search_bar">
              <div></div>
              <div>内部用户开店</div>
            </div>
            <div class="dialog-close" @click="dialogVisible = false">
              <img src="@/assets/images/close.png" alt="" />
            </div>
          </div>
          <div></div>
        </div>
        <div class="dialog-body center">
          <div class="company-info">
            <div>
              <span>企业名称：</span><span>{{ userInfo.orgName }}</span>
            </div>
          </div>
          <el-form
            :model="form"
            ref="form"
            :rules="rules"
            label-position="top"
            :inline="false"
            size="normal"
          >
            <div class="row dfb">
              <div class="col">
                <el-form-item label="店铺名称：" prop="shopName">
                  <el-input
                    clearable
                    v-model="form.shopName"
                    placeholder="请输入店铺名称"
                  />
                </el-form-item>
              </div>
              <div class="col">
                <el-form-item label="联系人：" prop="contact">
                  <el-input
                    clearable
                    v-model="form.contact"
                    placeholder="请输入联系人"
                  />
                </el-form-item>
              </div>
            </div>
            <div class="row dfb">
              <div class="col">
                <el-form-item label="联系电话：" prop="tel">
                  <el-input
                    clearable
                    v-model="form.tel"
                    placeholder="请输入联系电话"
                  />
                </el-form-item>
              </div>
              <div class="col">
                <el-form-item label="税率：" prop="taxRate">
                  <div class="dfa">
                    <el-input
                      type="number"
                      style="width: 110px"
                      clearable
                      v-model="form.taxRate"
                      placeholder="输入税率"
                    />&nbsp;&nbsp;%
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="row">
              <el-form-item label="详细地址：" prop="detailedAddress">
                <el-input
                  clearable
                  type="textarea"
                  :auto-resize="false"
                  v-model="form.detailedAddress"
                  placeholder="请输入详细地址"
                />
              </el-form-item>
            </div>
            <div class="row">
              <el-form-item label="店铺简介：" prop="shopProfile">
                <el-input
                  clearable
                  type="textarea"
                  :auto-resize="false"
                  v-model="form.shopProfile"
                  placeholder="请输入店铺简介"
                ></el-input>
              </el-form-item>
            </div>
            <el-form-item prop="agreeTerm">
              <el-checkbox v-model="form.agreeTerm" :indeterminate="false">
                您确认阅读并接受<span
                  style="color: seagreen"
                  @click="this.form.agreeTerm = true"
                  >《慧采商城开店协议》</span
                >
              </el-checkbox>
            </el-form-item>
            <div class="btn center" @click="handleSubmit">提交</div>
          </el-form>
        </div>
      </el-dialog>
      <div class="logoBox dfc">
        <div class="content-box center dfb">
          <div class="content_left dfa">
            <img
              src="@/assets/images/logo_wz.png"
              alt=""
              class="pointer"
              @click="
                () => {
                  $router.push('/index');
                }
              "
            />
            <!--<div class="tablist dfa" v-if="$route.path !== '/index'">
                            <div class="tabItem" @click="() => { current = 0; $router.push('/mFront/mallIndex') }">
                                <span :style="{ color: current === 0 ? '#216EC6' : '' }">慧采商城</span>
                                <div v-if="current === 0"></div>
                            </div>
                            <div class="tabItem" @click="() => { current = 1; $router.push('/mFront/biddingIndex') }">
                                <span :style="{ color: current === 1 ? '#216EC6' : '' }">招采中心</span>
                                <div v-if="current === 1"></div>
                            </div>
                        </div>-->
            <img
              v-show="current === 0"
              src="@/assets/images/hcsc.png"
              @click="$router.push('/mFront/mallIndex')"
              alt=""
            />
            <img
              class="pointer"
              v-show="current === 1"
              src="@/assets/images/zczx.png"
              @click="$router.push('/mFront/biddingIndex')"
              alt=""
            />
            <img
              class="pointer"
              v-show="current === 2"
              src="@/assets/images/grzx.png"
              @click="$router.push('/user/userCenter')"
              alt=""
            />
          </div>

          <div
            class="content_right dfa"
            v-if="$route.path !== '/mFront/materialControl'"
          >
            <div class="dfa">
              <div class="sx dfc" @click="searchType.show = !searchType.show">
                {{ searchType.name
                }}<i
                  :class="
                    searchType.show ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                  "
                ></i>
              </div>
              <div
                class="searchType"
                v-if="searchType.show"
                @mouseleave="searchType.show = false"
              >
                <div @click="changeSearchType(1)">商品</div>
                <div @click="changeSearchType(2)">店铺</div>
              </div>
              <i class="el-icon-search"></i>
              <input
                type="text"
                v-model="mainKeyWords"
                :placeholder="searchPlaceholder"
              />
            </div>
            <button @click="handleSearch">搜索</button>
          </div>
        </div>
      </div>
    </nav>
    <el-dialog title="慧采商城开店协议" :visible.sync="showTerm">
      <span v-html="content"></span>
      <span slot="footer">
        <el-button @click="showTerm = false">取消</el-button>
        <el-button type="primary" @click="showTerm = false">确定</el-button>
      </span>
    </el-dialog>
<!--    <el-dialog-->
<!--      title="物资采购平台域名调整提示"-->
<!--      :visible.sync="showIpTerm"-->
<!--    style="height: 400px; "-->
<!--    :show-close="false"-->
<!--    lock-scroll: true-->
<!--    >-->
<!--    <div class="fixed-dialog">-->
<!--      <span>由于物资采购平台域名升级，原域名将在2025年3月10日将停止使用，请复制新域名：</span>-->
<!--      <a href="https://www.srbgwz.com/index" target="_blank">https://www.srbgwz.com/index</a>-->
<!--      <br><br>-->
<!--      <span style="color: red;">请前往新网址进行业务操作！</span>-->
<!--    </div>-->
<!--    </el-dialog>-->
    <UserImprove ref="userImproveRef"/>
  </div>
</template>
<script>
import { routerObj } from './routerObj.js'
import {
    getShopStateByUserId,
    getUserNameInfo,
} from '@/api/frontStage/mallWebHeader'
import UserImprove from './user-improve'
import {
    createShopInside,
    cutOrg,
    getCartNum,
    getMsgNum,
    loginOut,
} from '@/api/frontStage/userCenter'
import { mapState } from 'vuex'
import { findByProgramaKey, getCurrentStep } from '@/api/w/richContent'
import { getVersionByOrgId } from '@/api/shopManage/product/prodcutInventory'
export default {
    components: {
        UserImprove,
    },
    data () {
        return {
            rules: {
                shopName: [
                    { required: true, message: '请输入店铺名称', trigger: 'blur' },
                    { min: 1, max: 100, message: '超出范围', trigger: 'blur' },
                ],
                contact: [
                    { required: true, message: '请输入联系人', trigger: 'blur' },
                    { min: 1, max: 20, message: '超出范围', trigger: 'blur' },
                ],
                tel: [
                    { required: true, message: '请输入11位手机号', trigger: 'blur' },
                    {
                        pattern: /^1[3456789]\d{9}$/,
                        message: '请输入正确的手机号',
                        trigger: 'blur',
                    },
                ],
                //
                shopProfile: [
                    { required: true, message: '请输入店铺简介', trigger: 'blur' },
                    { min: 1, max: 2500, message: '超出范围', trigger: 'blur' },
                ],
                detailedAddress: [
                    { required: true, message: '请输入详细地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' },
                ],
                taxRate: {
                    required: true,
                    validator: this.validateTaxRate,
                    trigger: 'blur',
                },
            },
            form: {
                shopName: '',
                contact: '',
                tel: '',
                detailedAddress: '',
                agreeTerm: false,
                taxRate: null,
                shopProfile: '', // 店铺简介
            },
            loginData: {},
            receiveType: 1,
            current: 10,
            showTerm: false,
            showIpTerm: false,
            content: '',
            platformYearFeeRecord: null,
            searchPlaceholder: '海量优质商品等您来选',
            searchType: {
                name: '商品',
                show: false,
            },
            msgList: [],
            cartNum: null,
            currentStep: null,
            shuDaoFlag: null,
            cartNumLoading: false,
            selectedEnterprise: '',
            dropdownList: [
                { label: '成都路桥', value: 1 },
                { label: '企业1', value: 2 },
                { label: '企业2', value: 3 },
                { label: '企业3', value: 4 },
            ],
            mainKeyWords: null,
            shopLoading: false,
            dialogVisible: false,
        }
    },
    watch: {
        $route: {
            handler (route) {
                let { keywords } = this.$route.query
                this.mainKeyWords = keywords || ''
                let pathArr1 = [
                    '/mFront/mallIndex',
                    '/mFront/shopList',
                    '/mFront/productList',
                ]
                let pathArr2 = [
                    '/mFront/biddingIndex',
                    '/mFront/order',
                    '/mFront/biddingDetail',
                    '/mFront/biddingDisplayDetail',
                ]
                // let pathArr3 = ['/mFront/materialControl']
                if (pathArr1.includes(route.path)) return (this.current = 0)
                if (pathArr2.includes(route.path)) return (this.current = 1)
                if (route.path.split('/')[1] === 'user' && route.path !== '/user/cart')
                    return (this.current = 2)
                this.current = 10
            },
            immediate: true,
        },
    },
    computed: {
        ...mapState(['userInfo']),
    },
    methods: {
        getVersionByOrgId () {
            getVersionByOrgId({ orgId: this.userInfo.orgId }).then(res=>{
                console.log(res)
            })
        },
        improveUserInfo (loginInfo) {
            console.log('=================>', loginInfo)
            console.log('this.$refs', this.$refs)
            this.$refs.userImproveRef.init(loginInfo)
        },
        //域名跳转
        skipNewDomain () {
        },
        inspectionDomain () {
            const domainWithProtocol = window.location.origin
            console.log(domainWithProtocol, 11, this.imgUrlPrefixAdd)
            if (!domainWithProtocol.includes('srbgwz.com')) {
            // 如果包含，则将 showIpTerm 设置为 true，显示对话框
                this.showIpTerm = true
            }
            console.log(!domainWithProtocol.includes('srbgwz.com'), 11, this.showIpTerm)
        },
        // eslint-disable-next-line
    validateTaxRate(rule, value, callback) {
            if (isNaN(Number(value))) {
                this.form.taxRate = ''
                return callback(new Error('请输入正确的税率'))
            }
            if (!value) return callback(new Error('请输入税率'))
            let lessThan = parseInt(value) < 0
            let biggerThan = parseInt(value) > 100
            if (lessThan || biggerThan) {
                lessThan ? (this.form.taxRate = 0) : ''
                biggerThan ? (this.form.taxRate = 100) : ''
                return callback(new Error('超出限制'))
            }
            let longFloatStr =
        typeof value === 'string' &&
        value.includes('.') &&
        value.split('.')[1].length > 2
            if (longFloatStr)
                this.form.taxRate = parseFloat(parseFloat(value).toFixed(2))
            callback()
        },
        async getRegisterAgreeUser (programaKey) {
            let res = await findByProgramaKey({ programaKey })
            this.fileList = res.files
            this.content = res.content
        },
        refreshHeaderVueM () {
            this.loginData = {}
        },
        menuOrgClick (item) {
            // if (item.orgName === this.userInfo.enterpriseName) {
            //     return this.$message.info('已是当前企业无需切换！')
            // }
            this.shopLoading = true
            item.userId = this.userInfo.userId
            cutOrg(item)
                .then(res => {
                    if (res != null && res.code == null) {
                        this.$store.commit('setRouterObj', routerObj)
                        this.$store.commit('setUserInfo', res)
                        location.reload()
                    }
                })
                .finally(() => {
                    this.shopLoading = false
                })
        },
        // 内部开店
        createShopInsideM () {
            createShopInside(this.form).then(res => {
                if (res.code === 200) {
                    this.$message({ message: '开店成功等待审核！', type: 'success' })
                    setTimeout(function () {
                        location.reload()
                    }, 1500)
                }
            })
        },
        // 提交
        handleSubmit () {
            this.$refs['form'].validate(valid => {
                if (!valid) return
                if (!this.form.agreeTerm)
                    return this.$message.error('请查看协议后勾选')
                if (valid) {
                    this.createShopInsideM()
                }
            })
        },
        async getMessageNum () {
            this.msgList = await getMsgNum({ receiveType: this.receiveType })
        },
        getCartNumM () {
            getCartNum().then(res => {
                if (res.code === 401) {
                    this.loginData = {}
                } else {
                    this.cartNum = res.code ? res.data : res
                    this.getShopStateByUserIdM()
                    this.getMessageNum()
                }
            })
        },
        dropChange (name) {
            this.selectedEnterprise = name
        },
        changeSearchType (num) {
            if (num === 1) {
                this.searchPlaceholder = '海量优质商品等您来选'
                this.searchType.name = '商品'
            } else {
                this.searchPlaceholder = '海量优质商家等您来选'
                this.searchType.name = '店铺'
            }
            this.searchType.show = false
        },
        handleSearch () {
            this.current = 0
            let type = this.searchType.name
            let routeParams = { path: type === '店铺' ? '/mFront/shopList' : '/mFront/productList', query: { keywords: this.mainKeyWords } }
            // type === '店铺' ? this.$parent.reverseKey() : ''
            this.$route.path === routeParams.path ? this.$router.push(routeParams) : this.openWindowTab(routeParams)
        },
        // 退出登录
        handleLogout () {
            loginOut().then(() => {
                localStorage.removeItem('token')
                this.$store.commit('setUserInfo', {})
                window.location.href = '/'
            })
            // 状态保持清除后刷新页面
        },
        goTo (path) {
            this.openWindowTab(path)
        },
        goCreatShop () {
            // 是否内部用户
            let isInterior = this.userInfo.isInterior === 1
            // 内部用户展示弹窗，否则跳转开店页面
            isInterior
                ? (this.dialogVisible = true)
                : this.openWindowTab('/mFront/openShop')//申请开店
        },
        goAuthShop () {
            // 是否内部用户
            let isInterior = this.userInfo.isInterior === 1
            if(!isInterior) {
                this.openWindowTab('/mFront/contractSigning')//店铺合同签约及缴费
            }
        },
        goAuthShop1 () {
            this.openWindowTab('/mFront/PlatformReview')
        },
        goFeedbackCenter () {
            this.openWindowTab({ path: '/user/feedback' })
        },
        goMainShop () {
            this.openWindowTab({
                path: '/mFront/shopIndex',
                query: { shopId: this.userInfo.shopId },
            })
        },
        async getShopStateByUserIdM () {
            this.shopLoading = true
            if (this.loginData.isShpAuthority === 1) {
                let res = await getShopStateByUserId()
                if (!res) return
                const keys = ['auditStatus', 'shopClass', 'shopId', 'isBusiness', 'state']
                keys.forEach(key => this.loginData[key] = res[key])
                this.$store.commit('setRouterObj', routerObj)
                this.$store.commit('setUserInfo', this.loginData)
                let res2 = await getCurrentStep()
                this.currentStep = res2.currentStep
                this.shuDaoFlag = res2.shuDaoFlag
                this.platformYearFeeRecord = res2.platformYearFeeRecord
            }
            let res = await getUserNameInfo()
            if (res) {
                this.loginData.userImg = res.userImg
                // this.loginData.userName = res2.nickName
                this.$store.commit('setRouterObj', routerObj)
                this.$store.commit('setUserInfo', this.loginData)
            }
            this.shopLoading = false
        },
        toChuShen () {
            this.$message.success('平台初审中')
            this.openWindowTab({ path: '/mFront/register' })
        },
    },
    created () {
        let { keywords } = this.$route.query
        this.mainKeyWords = keywords || ''
        //域名检查 2025-2-5 切换域名（https://mmcp.scrbg.com/index 切换为 https://mmcp.srbgwz.com）
        this.inspectionDomain()
        this.selectedEnterprise = this.userInfo.orgName
        this.dropdownList = this.userInfo.organizationVOS
        this.loginData.userName = this.userInfo.userName
        this.getRegisterAgreeUser('becomeSeller')
        let { userId, organizationVOS } = this.userInfo
        if (localStorage.getItem('token') && userId && !this.$route.query.token) {
            this.loginData = this.userInfo
            this.selectedEnterprise = this.loginData.orgName
            // 查询店铺状态
            this.getCartNumM()
            if (!organizationVOS) return
            this.dropdownList = organizationVOS.filter(item => item.orgName.length > 0)
        }
        //this.getVersionByOrgId()
    },
    mounted () {
        window.refreshHeaderVueM = this.refreshHeaderVueM
        // 监听事件总线
        this.$bus.$on('refreshUserInfo', () => (this.loginData = this.userInfo))
        this.$bus.$on('refreshCart', () => {
            getCartNum().then(res => (this.cartNum = res.code == null ? res : res.data))
        })
        // 图片选项切换
        let [box, left, right] = ['iconBox', 'arrow-left', 'arrow-right'].map(id => document.querySelector(`#${id}`))
        if (box == null || box.childNodes == null) return
        let len = box.childNodes.length

        if (len < 4) {
            left.style.display = 'none'
            right.style.display = 'none'
        } else {
            left.addEventListener('click', () => {
                box.scrollLeft -= 105
                if (box.scrollLeft < 0) box.scrollLeft = 0
            })
            right.addEventListener('click', () => {
                if (box.scrollLeft > (len - 4) * 100) return
                box.scrollLeft += 105
            })
        }
        this.improveUserInfo(this.loginData)
    },
}
</script>
<style scoped lang="scss">
/deep/ input[type='number'] {
  -moz-appearance: textfield !important;
  -webkit-appearance: none !important;
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/deep/ input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
nav {
  .content-box {
    width: 1326px;
    min-width: 1326px;

    & > div:last-child {
      padding: 32px 0;
    }

    .btns-user {
      height: 45px;
      font-size: 14px;

      img,
      span {
        cursor: pointer;
      }

      .user-left {
        height: 16px;

        & > div:first-child {
          height: 16px;

          img {
            width: 18px;
            margin-right: 9px;
          }

          span {
            //height: 16px;
            margin-right: 40px;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
      }

      .user-right {
          height: 100%;
          display: flex;
          align-items: center;
          color: #fff;
          .dfa:not(:last-of-type) {
              margin-right: 20px;
        }
        /deep/ .el-dropdown__caret-button::before {
            display: none;
        }
        .textOverflow1 {
            max-width: 40vw;
        }
        /deep/ .el-dropdown {
          button:first-of-type {
            display: none;
          }

          button:last-of-type {
            padding: 0;
            border: none;
            //color: rgba(33, 110, 198, 1);
            background-color: transparent;

            span {
              display: none;
            }

            &:after {
              content: '【切换】';
              color: #fff;
            }
          }
        }

        .order-link {
          margin-right: 20px;
        }

        .bell,
        .cart {
          position: relative;

          img {
            width: 16px;
            height: 16px;
            margin: 0;
          }

          .mark {
            width: 12px;
            height: 12px;
            line-height: 12px;
            text-align: center;
            border-radius: 50%;
            color: #fff;
            background-color: #f73838;
            position: absolute;
            top: -4px;
            right: -6px;

            div {
              transform: scale(0.7);
            }
          }
        }

        .cart {
          margin-right: 20px;

          span {
            margin-right: 5px;
          }
        }

        .greetings {
          height: inherit;
          margin-right: 20px;
          display: flex;
          justify-content: right;

          .pop {
            min-width: 117px;
            padding-left: 10px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 101;

            img {
              margin-left: 5px;
            }

            &:hover {
              & .pop-menu {
                display: block;
              }
            }
            .pop-menu {
              width: 385px;
              height: 270px;
              font-size: 14px;
              border: 1px solid rgba(230, 230, 230, 1);
              color: #333;
              background-color: #fff;
              position: absolute;
              z-index: 100;
              display: none;
              top: 43px;
              right: 0;

              .popTop {
                height: 137px;
                padding: 15px 18px 0;
                border-bottom: 1px solid rgba(230, 230, 230, 1);
                position: relative;

                .user {
                  height: 60px;
                  margin-bottom: 16px;

                  img {
                    width: 60px;
                    height: 60px;
                    margin-right: 12px;
                    border-radius: 50%;
                  }

                  .info {
                    padding-top: 6px;

                    .nameTitle,
                    .idTitle {
                      text-align: left;
                      max-width: 230px;
                    }

                    .nameTitle {
                      line-height: 24px;
                      margin-bottom: 4px;
                      font-size: 16px;
                      font-weight: 700;
                      color: rgba(56, 56, 56, 1);
                    }

                    .idTitle {
                      line-height: 21px;
                      font-size: 14px;
                      font-weight: 400;
                      color: rgba(153, 153, 153, 1);
                    }
                  }
                }

                .exit {
                  font-size: 14px;
                  color: rgba(33, 110, 198, 1);
                  position: absolute;
                  top: 8px;
                  right: 17px;
                  cursor: pointer;
                }

                .btns {
                  div {
                    height: 30px;
                    margin-right: 10px;
                    padding: 0 8px 0 10px;
                    border: 1px solid rgba(229, 229, 229, 1);
                    color: rgba(153, 153, 153, 1);
                    user-select: none;
                    cursor: pointer;
                  }
                }
              }

              .popBottom {
                padding: 16px 10px 0;

                .icons {
                  .iconBox {
                    width: 293px;
                    margin: 0 auto;
                    overflow-x: scroll;

                    &::-webkit-scrollbar {
                      display: none;
                    }

                    .items {
                      width: 84px !important;
                      font-size: 14px;
                      color: rgba(153, 153, 153, 1);
                      flex-direction: column;

                      div {
                        width: 84px;
                        height: 38px;
                      }
                    }

                    & .items:not(:last-of-type) {
                      margin-right: 21px;
                    }
                  }

                  i {
                    margin-top: 20px;
                    color: rgba(153, 153, 153, 1);
                  }

                  img {
                    width: 56px;
                    height: 56px;
                    margin-bottom: 5px;
                    border-radius: 50%;
                  }
                }
              }
            }
          }
        }

        .login-btns {
          // width: 322px;
          height: 14px;
          margin-right: 10px;
          line-height: 16px;
          font-size: 14px;
          letter-spacing: 0;
          font-weight: 400;
        }

        img {
          margin-right: 6px;
        }
      }
    }
  }

  .topBar {
    color: #fff;
    background-color: #101010;

    .content-box {
      & > div:last-child {
        padding: 0;
      }
    }
  }
}

.logoBox {
  background: #fff;
  height: 130px;
  width: 100%;

  .content_left {
    img:first-child {
      width: 388px;
      height: 70px;
      margin-right: 16px;
    }
    img {
      width: 97px;
      height: 42px;
    }
    //.tablist {
    //margin-left: 75px;

    .tabItem {
      font-size: 18px;
      color: #000000;
      letter-spacing: 0;
      font-weight: 400;
      margin-right: 40px;
      height: 28px;
      cursor: pointer;

      div {
        background: #226fc7;
        width: 72px;
        height: 3px;
        margin-top: 5px;
      }
    }
    //}
  }

  .content_right {
    .dfa {
      width: 350px;
      height: 40px;
      background: #fafafa;
      border: 1px solid rgba(222, 222, 222, 1);
      padding-left: 10px;
      position: relative;
      justify-content: flex-start;

      i {
        color: #999;
        margin: 0 10px 0 4px;
      }

      .sx {
        width: 68px;
        height: 30px;
        border-right: solid 1px #dedede;
        // background-color: beige;
        margin-right: 15px;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        font-weight: 500;
        cursor: pointer;
        // &:active .searchType{display: block;}
      }

      .searchType {
        width: 86px;
        height: 74px;
        position: absolute;
        border: 1px solid #dedede;
        left: 0;
        bottom: -74px;
        z-index: 3;
        cursor: pointer;

        div {
          height: 36px;
          line-height: 36px;
          text-align: center;
          background-color: #fff;
          user-select: none;

          &:hover {
            color: rgb(33, 110, 198);
          }
        }
      }
    }

    button {
      height: 40px;
      text-align: center;
      line-height: 40px;
      background: #216ec6;
      width: 80px;
      color: #fff;
    }

    input,
    button {
      //
    }
  }
}

.dialog-login {
  .login-box {
    width: 400px;
    margin-top: 80px;
    margin-bottom: 20px;

    .tabs {
      width: 270px;
      height: 36px;
      font-size: 20px;
      margin-bottom: 28px;
      color: #333333;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      font-weight: 400;
      position: relative;
      cursor: pointer;

      .first,
      .second {
        width: 135px;
      }

      .first {
        text-align: left;
      }

      .second {
        text-align: right;
      }

      .active-bar {
        width: 104px;
        height: 3px;
        background: #226fc7;
        position: absolute;
        left: 8px;
        bottom: 0;
        transition: 0.3s;
      }
    }

    .el-form-item.el-form-item--normal {
      margin: 0;
      position: relative;

      .msg {
        color: red;
        position: absolute;
        left: 3px;
        bottom: 0;
      }
    }

    .el-input.el-input--normal {
      width: 100%;
      border: none;
    }

    /deep/ .el-input--normal {
      width: 400px;
      height: 55px;
      margin-bottom: 30px;
    }

    /deep/ .el-input__inner {
      width: 400px;
      height: 55px;
      padding: 0 40px 0 50px;
      font-size: 20px;
      border: 1px solid #d9d9d9;
      border-radius: 0;
      display: flex;

      // align-items: center;
      ::-webkit-input-placeholder {
        font-size: 20px;
        color: #d9d9d9;
      }
    }

    .input-icon {
      width: 15px;
      height: 20px;
      margin-right: 10px;

      &:last-child {
        width: unset;
        height: unset;
        margin: 0 0 0 10px;
      }
    }

    .input-box {
      width: 400px;
      height: 55px;
      margin-bottom: 30px;
      padding: 0 12px;
      border: 1px solid #d9d9d9;
      display: flex;
      align-items: center;

      input {
        height: 53px;
        flex-grow: 1;
      }
    }

    .verify-box {
      height: 55px;
      margin-bottom: 30px;

      /deep/ .el-input--normal {
        width: 270px;
        margin-bottom: 0;
      }

      /deep/ .el-input__inner {
        width: 270px;
        margin: 0 10px 0 0 !important;
        margin-bottom: unset;
      }

      button {
        width: 120px;
        font-size: 16px;
        border: 1px solid #216ec6;
        border-radius: 5px;
        color: #216ec6;
        background: #ffffff;

        &:active {
          color: #fff;
          background-color: #216ec6;
        }
      }
    }

    button {
      width: 400px;
      height: 55px;
      font-size: 20px;
      color: #fff;
      background-color: #216ec6;
    }
  }
}

/deep/ .el-dialog {
  width: 800px !important;
  height: 750px !important;
  margin-top: 161px !important;

  .dialog-body {
    width: 630px;
    padding: 50px 0 10px;

    .company-info {
      height: 45px;
      margin-bottom: 30px;
      padding: 15px;
      background: #f7f7f7;

      div {
        font-size: 14px;

        span:first-child {
          color: #999;
        }

        span:last-child {
          color: #226fc7;
        }
      }
    }

    .row {
      margin-bottom: 30px;

      .col {
        width: 300px;
      }

      .el-input__inner {
        height: 35px;
        border-radius: 0;
      }

      .el-textarea__inner {
        height: 70px !important;
        padding: 11px 10px;
        border-radius: 0;
        resize: none;
      }
    }

    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__label {
      height: 24px;
      line-height: 14px;
      color: #999;
    }

    .btn {
      width: 80px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      text-align: center;
      color: #fff;
      background-color: #216ec6;
      cursor: pointer;
      user-select: none;
    }
  }
}
</style>
<style lang="scss">
ul.el-dropdown-menu--small {
  width: 260px;
  margin-right: -100px;

  &,
  .el-dropdown-menu__item {
    padding: 0 10px;
  }

  .el-dropdown-menu__item {
    height: 45px;
    line-height: 45px;

    &:not(:last-of-type) {
      border-bottom: 1px solid rgba(230, 230, 230, 1);
    }

    position: relative;

    &:hover {
      background-color: #fff;
    }

    img {
      width: 16px;
      height: 16px;
      position: absolute;
      top: 14px;
      right: 4px;
    }
  }

  .popper__arrow {
    display: none !important;
  }
}
</style>
