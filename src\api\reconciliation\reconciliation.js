import service from '@/utils/request'

// eslint-disable-next-line no-unused-vars
const { httpPost, httpGet } = service
const materialReconciliationPageList = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/listByEntity',
        params
    })
}
const materialReconciliationIds = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/ListByIds',
        params
    })
}

const materialReconciliationDtlIds = params => {
    return httpPost({
        url: '/materialMall/materialReconciliationDtl/ListByBillIds',
        params
    })
}

//申请发票，查询二级对账单可开发发票数据
const supplierReconciliationDtlIds = params => {
    return httpPost({
        url: '/materialMall/supplierReconciliation/ListByBillIds',
        params
    })
}

const synthesizeTemporaryListByEntity = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/synthesizeTemporaryListByEntity',
        params
    })
}
const supplierListByEntity = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/supplierListByEntity',
        params
    })
}
const supplierSTListByEntity = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/supplierSTListByEntity',
        params
    })
}
const getContactPlanPageList = params => {
    return httpPost({
        url: '/materialMall/performanceManage/orderSelectPlan/getContactPlanPageList',
        params
    })
}

const supplierGetContactPlanPageList = params => {
    return httpPost({
        url: '/materialMall/performanceManage/orderSelectPlan/supplierGetContactPlanPageList',
        params
    })
}
/**
 * 供应商查询采购机构公司分组
 * @param params
 * @returns {*}
 */
const supplierGetEnterprisePageList = params => {
    return httpPost({
        url: '/materialMall/performanceManage/orderSelectPlan/supplierGetEnterprisePageList',
        params
    })
}

/**
 * 采购机构查询供应商公司分组
 * @param params
 * @returns {*}
 */
const getContactSupplierPageList = params => {
    return httpPost({
        url: '/materialMall/performanceManage/orderSelectPlan/getContactSupplierPageList',
        params
    })
}

const getCanUseSiteReceivingDtl = params => {
    return httpPost({
        url: '/PCWP2/thirdapi/siteReceiving/getCanUseSiteReceivingDtl',
        params
    })
}
const materialReconciliationGetOrderById = params => {
    return httpGet({
        url: '/materialMall/materialReconciliation/getOrderById',
        params
    })
}
const getOrderItem4Price = params => {
    return httpPost({
        url: '/materialMall/materialReconciliation/getOrderItem4Price',
        params
    })
}
const materialReconciliationCreate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationCreate',
        params
    })
}

//根据计划编号查询订单状态
const selectOrderListByPlanNo = params => {
    return httpPost({
        url: '/materialMall/performanceManage/orderSelectPlan/selectOrderListByPlanNo',
        params
    })
}
const materialReconciliationSTCreate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSTCreate',
        params
    })
}
const materialReconciliationSTSupplierCreate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSTSupplierCreate',
        params
    })
}
const materialReconciliationSupplierCreate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSupplierCreate',
        params
    })
}
const materialReconciliationUpdate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationUpdate',
        params
    })
}
const materialReconciliationSTUpdate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSTUpdate',
        params
    })
}
const materialReconciliationSupplierUpdate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSupplierUpdate',
        params
    })
}
const materialReconciliationSTSupplierUpdate = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSTSupplierUpdate',
        params
    })
}
const materialReconciliationDelete = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliationDelete',
        params
    })
}
const getOrderItemPriceByMas = params => {
    return httpPost({
        url: '/materialMall/shopManage/orders/getOrderItemPriceByMas',
        params
    })
}
const materialReconciliationSTDelete = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliationSTDelete',
        params
    })
}
const materialReconciliationAffirm = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliationAffirm',
        params
    })
}
const materialReconciliationSupplierAffirm = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliationSupplierAffirm',
        params
    })
}
const materialReconciliationCancellation = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationCancellation',
        params
    })
}
const materialReconciliationSTCancellation = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSTCancellation',
        params
    })
}
const outputExcel = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliation/outputExcel',
        params,
        responseType: 'blob',
    })
}
const outputSTExcel = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliation/outputSTExcel',
        params,
        responseType: 'blob',
    })
}
const materialReconciliationGetBySn = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliationGetBySn',
        params
    })
}
const materialReconciliationPushAcceptance = params => {
    return httpGet({
        url: '/materialMall/performanceManage/materialReconciliationPushAcceptance',
        params
    })
}
const materialReconciliationSubmitCheck = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSubmitCheck',
        params
    })
}
const materialReconciliationSTSubmitCheck = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSTSubmitCheck',
        params
    })
}
const materialReconciliationSubmit = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSubmit',
        params
    })
}
const materialReconciliationAuditPlan = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationAuditPlan',
        params
    })
}
const materialReconciliationSTAuditPlan = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliationSTAuditPlan',
        params
    })
}
// 物资对账台账单(后台管理平台)
const materialReconciliationLedger = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/materialReconciliationLedger',
        params
    })
}
// 物资对账台账单(后台管理平台)(机构)
const materialReconciliationOrgLedger = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/materialReconciliationOrgLedger',
        params
    })
}
// 物资对账台账单(供应商履约平台)(物资维度)
const materialReconciliationLedgerSupplier = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/materialReconciliationLedgerSupplier',
        params
    })
}
// 物资对账台账单(供应商履约平台)(账单维度)
const materialReconciliationLedgerSupplierBill = params => {
    return httpPost({
        url: '/materialMall/performanceManage/materialReconciliation/materialReconciliationLedgerSupplierBill',
        params
    })
}
// 采购方-查询可对账的计划订单列表
const getReconciliablePlansByEnterprisePageList = params => {
    return httpPost({
        url: '/materialMall/planReconciliation/getReconciliablePlansByEnterprisePageList',
        params: params
    })
}
// 采购方-查询可对账的供应商列表
const getReconciliableSupplierByEnterprisePageList = params => {
    return httpPost({
        url: '/materialMall/planReconciliation/getReconciliableSupplierByEnterprisePageList',
        params: params
    })
}
export {

    materialReconciliationPageList,
    materialReconciliationDtlIds,
    materialReconciliationIds,
    getContactPlanPageList,
    getCanUseSiteReceivingDtl,
    materialReconciliationSTAuditPlan,
    materialReconciliationCreate,
    materialReconciliationGetBySn,
    materialReconciliationSubmitCheck,
    materialReconciliationAuditPlan,
    materialReconciliationUpdate,
    supplierReconciliationDtlIds,
    materialReconciliationDelete,
    materialReconciliationSTSupplierCreate,
    materialReconciliationAffirm,
    materialReconciliationGetOrderById,
    materialReconciliationSTDelete,
    materialReconciliationSTCreate,
    selectOrderListByPlanNo,
    materialReconciliationSTSupplierUpdate,
    supplierListByEntity,
    supplierSTListByEntity,
    materialReconciliationSTCancellation,
    getOrderItem4Price,
    materialReconciliationSTUpdate,
    supplierGetContactPlanPageList,
    supplierGetEnterprisePageList,
    getContactSupplierPageList,
    synthesizeTemporaryListByEntity,
    materialReconciliationSupplierCreate,
    materialReconciliationSubmit,
    outputExcel,
    materialReconciliationSupplierUpdate,
    materialReconciliationSTSubmitCheck,
    materialReconciliationSupplierAffirm,
    getOrderItemPriceByMas,
    materialReconciliationPushAcceptance,
    outputSTExcel,
    materialReconciliationCancellation,
    materialReconciliationLedger,
    materialReconciliationLedgerSupplier,
    materialReconciliationLedgerSupplierBill,
    getReconciliablePlansByEnterprisePageList,
    getReconciliableSupplierByEnterprisePageList,
    materialReconciliationOrgLedger,
}
