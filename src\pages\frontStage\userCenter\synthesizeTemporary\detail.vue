<template>
    <main>
        <div v-loading="synthesizeLoading">
            <div class="list-title dfa mb20">大宗临购清单</div>
            <el-form :inline="true" ref="synthesizeRef" :model="synthesizeFormData" style="margin-left: 20px" :rules="synthesizeRoles">
                <el-row>
                    <el-col :span="12" >
                        <el-form-item label="单据编号：" prop="synthesizeTemporarySn">
                            {{synthesizeFormData.synthesizeTemporarySn}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="synthesizeFormData.state === 3 || synthesizeFormData.state === 6">
                        <el-form-item label="超期垫资利息（%）：" prop="outPhaseInterest">
                            {{synthesizeFormData.outPhaseInterest}}%
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" >
                        <el-form-item label="供应商单位：" prop="supplierOrgName">
                            {{synthesizeFormData.supplierOrgName}}
<!--                            <el-input  style="width: 300px" disabled v-model="synthesizeFormData.supplierOrgName"></el-input>-->
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="采购单位：" prop="orgName">
                            {{synthesizeFormData.orgName}}
<!--                            <el-input  style="width: 300px" disabled v-model="synthesizeFormData.orgName"></el-input>-->
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" >
                        <el-form-item label="清单类型：" prop="billType">
                            <el-radio :disabled="!(synthesizeFormData.state == 0 || synthesizeFormData.state == 11)" v-model="synthesizeFormData.billType" :label="1">浮动价格</el-radio>
                            <el-radio :disabled="!(synthesizeFormData.state == 0 || synthesizeFormData.state == 11)" v-model="synthesizeFormData.billType" :label="2">固定价格</el-radio>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" >
                        <el-form-item label="货款支付周期（月）：" prop="paymentWeek">
                            <el-select  :disabled="!(synthesizeFormData.state == 0 || synthesizeFormData.state == 11) && disabledElement" v-model="synthesizeFormData.paymentWeek" clearable placeholder="请选择货款支付周期">
                                <el-option v-for="item in payWeekList" :key="item.value" :label="item.label"
                                           :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="项目收货地址选择：" prop="province">
                            <el-cascader
                                :disabled="!(synthesizeFormData.state == 0 || synthesizeFormData.state == 11) && disabledElement"
                                clearable
                                style="width: 300px"
                                size="large"
                                :options="addressData"
                                v-model="selectAddressOptions"
                                @change="handleAddressChange">
                            </el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item  label="项目收货详细地址：" prop="receiverAddress">
                            <el-input :disabled="!(synthesizeFormData.state == 0 || synthesizeFormData.state == 11) && disabledElement" clearable v-model="synthesizeFormData.receiverAddress" style="width: 300px" placeholder="请输入项目收货地址"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="单据状态：">
                            <el-tag type="info" v-if="synthesizeFormData.state == 0">草稿</el-tag>
                            <el-tag v-if="synthesizeFormData.state == 1">已提交</el-tag>
                            <el-tag v-if="synthesizeFormData.state == 2">已提交</el-tag>
                            <el-tag type="success" v-if="synthesizeFormData.state == 3">供应商已确认</el-tag>
                            <el-tag type="danger" v-if="synthesizeFormData.state == 4">审核不通过</el-tag>
                            <el-tag type="danger" v-if="synthesizeFormData.state == 11">审核不通过</el-tag>
                            <el-tag type="danger" v-if="synthesizeFormData.state == 5">已拒绝</el-tag>
                            <el-tag type="success" v-if="synthesizeFormData.state == 6">已推送大宗临购计划</el-tag>
                        </el-form-item>
                    </el-col>
                    <!-- <el-col :span="12">
                        <el-form-item label="含税总金额（元）：">
                            <span style="color: red">{{synthesizeFormData.synthesizeSumAmount}}</span>
                        </el-form-item>
                    </el-col> -->
                    <el-col :span="12" >
                        <el-form-item label="用户类型：" prop="billType">
                            <span v-if="synthesizeFormData.orgFarId">内部用户</span>
                            <span v-else>外部用户</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12" v-if="synthesizeFormData.state === 3 || synthesizeFormData.state === 6">
                        <el-form-item label="供方单位确认时间：" prop="auditTime">
                            {{synthesizeFormData.auditTime}}
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="创建时间：" prop="gmtCreate">
                            {{synthesizeFormData.gmtCreate}}
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注：" prop="remarks" >
                            <el-input :disabled="!(synthesizeFormData.state == 0 || synthesizeFormData.state == 11) && disabledElement" style="width: 600px;" type="textarea" :auto-resize="false" v-model="synthesizeFormData.remarks"
                                      placeholder="请输入备注" maxlength="1000" show-word-limit></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                border
                ref="msgTable"
                :data="synthesizeFormData.dtls"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="materialName" label="物资名称" width=""/>
                <el-table-column prop="spec" label="规格型号" width=""/>
                <el-table-column prop="texture" label="材质" width=""/>
                <el-table-column prop="brandName" label="品牌" width=""/>
                <el-table-column prop="unit" label="单位" width=""/>
<!--                <el-table-column prop="isTwoUnit" label="是否有副级单位" width="">-->
<!--                    <template v-slot="scope">-->
<!--                        <el-tag type="success" v-if="scope.row.isTwoUnit === 1">是</el-tag>-->
<!--                        <el-tag type="info" v-if="scope.row.isTwoUnit === 0">否</el-tag>-->
<!--                    </template>-->
<!--                </el-table-column>-->
                <el-table-column prop="qty" label="数量" width="">
                    <template v-slot="scope">
                        <el-input v-if="synthesizeFormData.state == 0 || synthesizeFormData.state == 11"
                            type="number"
                                  :disabled="disabledElement"
                            v-model="scope.row.qty"
                            @change="getChangedRow(scope.row)">
                        </el-input>
                        <span v-else>{{scope.row.qty}}</span>
                    </template>
                </el-table-column>
<!--                <el-table-column prop="twoUnitNum" label="副级数量" width="" v-if="synthesizeFormData.state == 0">-->
<!--                    <template v-slot="scope">-->
<!--                        <el-input-->
<!--                            v-if="synthesizeFormData.state == 0 && scope.row.isTwoUnit === 1"-->
<!--                            type="number"-->
<!--                            v-model="scope.row.twoUnitNum"-->
<!--                            @change="getChangedRow(scope.row)">-->
<!--                        </el-input>-->
<!--                        <span v-else-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnitNum}}</span>-->
<!--                        <span v-else>/</span>-->
<!--                    </template>-->
<!--                </el-table-column>-->
<!--                <el-table-column prop="twoUnit" label="副级单位" width="">-->
<!--                    <template v-slot="scope">-->
<!--                        <span v-if="scope.row.isTwoUnit === 1">{{scope.row.twoUnit}}</span>-->
<!--                        <span v-else>/</span>-->
<!--                    </template>-->
<!--                </el-table-column>-->
                <el-table-column prop="referenceAmount" label="平台参考价" width="100"/>
                <el-table-column v-if="synthesizeFormData.billType === 1 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="netPrice" label="网价" width="100">
                </el-table-column>
                <el-table-column v-if="synthesizeFormData.billType === 1 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="fixationPrice" label="固定费" width="100">
                </el-table-column>
                <el-table-column v-if="synthesizeFormData.billType === 2 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="outFactoryPrice" label="出厂价" width="100">
                </el-table-column>
                <el-table-column v-if="synthesizeFormData.billType === 2 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="transportPrice" label="运杂费" width="100">
                </el-table-column>
                <el-table-column prop="synthesizePrice" label="含税单价（元/单位）" width="100"/>
                <el-table-column prop="synthesizeAmount" label="含税金额（元）" width="100"/>
                <el-table-column label="操作" width="70" v-if="synthesizeFormData.state == 0">
                    <template slot-scope="scope">
                        <span class="action" :disabled="disabledElement"  @click="onDel(scope.row)">删除</span>
                    </template>
                </el-table-column>
                <template slot="append" v-if="synthesizeFormData.dtls && synthesizeFormData.dtls.length">
                    <tr class="summary-row">
                        <td :colspan="$refs.msgTable ? $refs.msgTable.columns.length : 1">
                            {{ getTableSummary({ columns: $refs.msgTable ? $refs.msgTable.columns : [], data: synthesizeFormData.dtls || [] }).filter(x => x).join('  ') }}
                        </td>
                    </tr>
                </template>
            </el-table>
            <div v-if="synthesizeFormData.quotations" class="list-title dfa mb20">专区报价详情</div>
            <el-form :inline="true" ref="synthesizeRef" :model="synthesizeFormData" style="margin-left: 20px" :rules="synthesizeRoles">
                <el-row>
                    <el-col :span="12" >
                        <el-form-item label="报价单编号：">
                            <span style="color: blue">{{synthesizeFormData.quotations.length > 0 ? synthesizeFormData.quotations[0].biddingSn : ''}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table
                border
                ref="msgTable"
                :data="synthesizeFormData.quotations"
                :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                :row-style="{ fontSize: '14px', height: '48px' }"
            >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="productName" label="物资名称" width=""/>
                <el-table-column prop="spec" label="规格型号" width=""/>
                <el-table-column prop="productTexture" label="材质" width=""/>
                <el-table-column prop="brand" label="品牌" width=""/>
                <el-table-column prop="unit" label="单位" width=""/>
                <el-table-column prop="num" label="数量" width=""/>
                <el-table-column prop="bidAmount" label="实际报价（不含税）" width=""/>
                <el-table-column v-if="synthesizeFormData.billType === 1 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="netPrice" label="网价" width="100">
                </el-table-column>
                <el-table-column v-if="synthesizeFormData.billType === 1 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="fixationPrice" label="固定费" width="100">
                </el-table-column>
                <el-table-column v-if="synthesizeFormData.billType === 2 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="outFactoryPrice" label="出厂价" width="100">
                </el-table-column>
                <el-table-column v-if="synthesizeFormData.billType === 2 && (synthesizeFormData.state == 3 || synthesizeFormData.state == 6)" prop="transportPrice" label="运杂费" width="100">
                </el-table-column>
                <el-table-column prop="bidRatePrice" label="含税单价（元/单位）" width="100"/>
                <el-table-column prop="bidRateAmount" label="含税金额（元）" width="100"/>
                <el-table-column label="操作" width="70" v-if="synthesizeFormData.state == 0">
                    <template slot-scope="scope">
                        <span class="action" :disabled="disabledElement"  @click="onDel(scope.row)">删除</span>
                    </template>
                </el-table-column>
                <template slot="append" v-if="synthesizeFormData.quotations && synthesizeFormData.quotations.length">
                    <tr class="summary-row">
                        <td :colspan="$refs.msgTable ? $refs.msgTable.columns.length : 1">
                            {{ getTableSummary({ columns: $refs.msgTable ? $refs.msgTable.columns : [], data: synthesizeFormData.quotations || [] }).filter(x => x).join('  ') }}
                        </td>
                    </tr>
                </template>
            </el-table>
            <div class="list-title dfa mb20" v-if="synthesizeFormData.state == 11">审核历史</div>
                <el-table
                    border
                    v-if="synthesizeFormData.state == 11"
                    height="200px"
                    :data="synthesizeFormData.auditRecords"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                <el-table-column label="序号" type="index" width="60"></el-table-column>
                <el-table-column prop="auditType" label="审核类型" width="160">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.auditType == 1">提交审核</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="founderName" label="审核人" width="200">
                </el-table-column>
                <el-table-column prop="gmtCreate" label="审核时间" width="160">
                </el-table-column>
                <el-table-column prop="auditResult" label="审核意见" width="">
                </el-table-column>
            </el-table>
            </div>
            <div class="buttons" style="float:right;margin-top: 20px;margin-bottom: 20px">
                <el-button v-if="synthesizeFormData.state == 0 || synthesizeFormData.state == 11" type="primary" class="btn-greenYellow" @click="updateSynthesizeTemporaryM" :disabled="disabledElement">保存</el-button>
                <el-button v-if="synthesizeFormData.state == 0 || synthesizeFormData.state == 11" type="primary" class="btn-greenYellow" @click="updateSynthesizeTemporaryM(1)" :disabled="disabledElement">保存并提交</el-button>
                <el-button v-if="synthesizeFormData.state == 0 || synthesizeFormData.state == 3 || synthesizeFormData.state == 11" type="primary" class="btn-delete" @click="deleteSynthesizeTemporaryM" :disabled="disabledElement">删除</el-button>
                <el-button v-if="synthesizeFormData.state == 3" type="primary" class="btn-delete" @click="refuseSynthesizeTemporaryM" :disabled="disabledElement">拒绝</el-button>
                <el-button v-if="synthesizeFormData.state == 3" type="primary"  @click="submitPlan" :disabled="disabledElement">推送大宗临购计划</el-button>
                <el-button type="primary" @click="outExcel">导出excel</el-button>
                <el-button @click="$router.go(-1)">返回</el-button>
            </div>
    </main>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import { toFixed } from '@/utils/common'
import {
    updateSynthesizeTemporary,
    submitPlan,
    exportExcel,
    deleteSynthesizeTemporary,
    synthesizeTemporaryGetBySn,
    deleteInfoItem,
    refuseBusiness,
} from '@/api/frontStage/userCenter'
// eslint-disable-next-line no-unused-vars
import { CodeToText, regionData, TextToCode } from 'element-china-area-data'
import { UserPermission } from '@/utils/permissions'
export default {
    filters: {
        dateStr (dateStr) {
            if(dateStr == null) {
                return ''
            }
            return dateStr.split('T')[0]
        }
    },
    name: 'detail',
    data () {
        return {
            synthesizeRoles: {
                receiverAddress: [
                    { required: true, message: '请输入地址', trigger: 'blur' },
                ],
                province: [
                    { required: true, message: '请选择地址', trigger: 'blur' },
                    { min: 1, max: 200, message: '超出范围', trigger: 'blur' }
                ],
            },
            payWeekList: [
                { label: '0月', value: 0 },
                { label: '1月', value: 1 },
                { label: '2月', value: 2 },
                { label: '3月', value: 3 },
                { label: '4月', value: 4 },
                { label: '5月', value: 5 },
                { label: '6月', value: 6 },
            ],
            synthesizeLoading: false,
            synthesizeFormData: {},
            // 地址
            addressData: regionData, // 地址数据
            selectAddressOptions: [],
            // 数据权限
            userPermission: new UserPermission('物资下单权限'),
            disabledElement: false
        }
    },
    created () {
        this.getFormDtl()
    },
    methods: {
        getTableSummary (param) {
            const { columns, data } = param
            const sums = []
            let amountSum = 0
            data.forEach(row => {
                var val = undefined
                if (row.synthesizeAmount) {
                    val = Number(row.synthesizeAmount)
                } else {
                    val = Number(row.bidRateAmount)
                }
                if (!isNaN(val)) {
                    amountSum += val
                }
            })
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计：'
                } else if (index === 1) {
                    sums[index] = amountSum.toFixed(2)
                } else {
                    sums[index] = ''
                }
            })
            return sums
        },
        outExcel () {
            this.synthesizeLoading = true
            let str = ''
            if(this.synthesizeFormData.billType == 1) {
                str = '大宗临购清单（浮动价格）.xlsx'
            }
            if(this.synthesizeFormData.billType == 2) {
                str = '大宗临购清单（固定价格）.xlsx'
            }
            exportExcel({ id: this.synthesizeFormData.synthesizeTemporaryId }).then(res=>{
                const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = str
                a.click()
                window.URL.revokeObjectURL(url)
                this.$message.success('操作成功')
            }).finally(()=>{
                this.synthesizeLoading = false
            })
        },
        onDel (row) {
            this.clientPop('info', '您确定要删除吗？', async () => {
                if(this.synthesizeFormData.dtls.length == 1) {
                    return this.$message.error('至少保留一项！')
                }
                this.synthesizeLoading = true
                deleteInfoItem({ id: row.synthesizeTemporaryDtlId }).then(res => {
                    if(res.code == 200) {
                        this.$message.success('操作成功！')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.synthesizeLoading = false
                })
            })
        },
        // 推送计划
        submitPlan () {
            this.clientPop('info', '您确定要推送大宗临购计划吗？', async () => {
                this.synthesizeLoading = true
                submitPlan({ id: this.synthesizeFormData.synthesizeTemporaryId }).then(res => {
                    if(res.code == 200) {
                        this.$message.success('推送大宗临购计划成功！请及时去PCWP审核计划')
                        this.getFormDtl()
                    }
                }).finally(() => {
                    this.synthesizeLoading = false
                })
            })
        },
        updateSynthesizeTemporaryM (num) {
            for (let i = 0; i < this.synthesizeFormData.dtls.length; i++) {
                let t = this.synthesizeFormData.dtls[i]
                if(t.qty == null || Number(t.qty) == 0) {
                    return this.$message.error('商品为【' + t.productName + '】数量不能为0！')
                }
            }
            this.$refs.synthesizeRef.validate(valid=>{
                if(valid) {
                    let str = ''
                    if(num != null && num == 1) {
                        str = '您确定要保存并提交吗？'
                    }else {
                        str = '您确定要保存吗？'
                    }
                    this.clientPop('info', str, async () => {
                        this.synthesizeLoading = true
                        if(num != null && num == 1) {
                            this.synthesizeFormData.isSubmit = 1
                        }
                        updateSynthesizeTemporary(this.synthesizeFormData).then(res => {
                            if(res.code == 200) {
                                this.getFormDtl()
                                this.$message.success('操作成功！')
                            }
                        }).finally(() => {
                            this.synthesizeLoading = false
                        })
                    })
                }else {
                    return this.$message.error('请检查非空项！')
                }
            })
        },
        deleteSynthesizeTemporaryM () {
            this.clientPop('info', '您确定要删除吗？', async () => {
                this.synthesizeLoading = true
                deleteSynthesizeTemporary({ id: this.synthesizeFormData.synthesizeTemporaryId }).then(res => {
                    if(res.code == 200) {
                        this.$message.success('操作成功！')
                        this.$router.go(-1)
                    }
                }).finally(() => {
                    this.synthesizeLoading = false
                })
            })
        },
        refuseSynthesizeTemporaryM () {
            this.clientPop('info', '您确定要拒绝该单据吗？', async () => {
                this.$prompt('拒绝原因', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    inputType: 'textarea',
                    inputPlaceholder: '请输入拒绝原因',
                    inputPattern: /^.+$/,
                    inputErrorMessage: '请输入拒绝原因'
                }).then(({ value }) => {
                    let params = {
                        id: this.synthesizeFormData.synthesizeTemporaryId,
                        isOpen: 0,
                        auditResult: value
                    }
                    this.synthesizeLoading = true
                    refuseBusiness(params).then(res => {
                        if (res.code != null && res.code == 200) {
                            this.$message.success('操作成功')
                            this.getFormDtl()
                        }
                    }).finally(() => {
                        this.synthesizeLoading = false
                    })
                }).catch(() => {
                })
            })
        },
        fixed4 (num) {
            return toFixed(num, 4)
        },
        fixed2 (num) {
            return toFixed(num, 2)
        },
        getChangedRow (row) {
            // if(row.isTwoUnit === 1) {
            //     if(row.twoUnitNum <= 0) {
            //         row.twoUnitNum = 0
            //     }
            //     if(row.twoUnitNum >= 999999) {
            //         row.twoUnitNum = 999999
            //     }
            //     row.twoUnitNum = this.fixed4(row.twoUnitNum)
            //     row.qty = this.fixed4(row.twoUnitNum / row.secondUnitNum)
            // }
            // if(row.isTwoUnit === 0) {
            if(row.qty <= 0) {
                row.qty = 0
            }
            if(row.qty >= 999999) {
                row.qty = 999999
            }
            row.qty = this.fixed4(row.qty)
            if(row.isTwoUnit === 1) {
                row.twoUnitNum = this.fixed4(row.qty * row.secondUnitNum)
            }
            // }
            let sumAmount = 0
            for (let i = 0; i < this.synthesizeFormData.dtls.length; i++) {
                let t = this.synthesizeFormData.dtls[i]
                let amount = this.fixed2(Number(t.qty) * Number(t.referencePrice))
                t.referenceAmount = amount
                t.synthesizeAmount = amount
                sumAmount = this.fixed2(Number(sumAmount) + (Number(amount)))
            }
            this.synthesizeFormData.referenceSumAmount = sumAmount
            this.synthesizeFormData.synthesizeSumAmount = sumAmount
        },
        // 地址回显
        addressFormatShow (row) {
            if(row.province == null) {
                return
            }
            //地址选择器回显
            //把省市区文字重新转化为代码
            let selected = TextToCode[row.province][row.city][row.county].code
            let selected1 = JSON.stringify(selected).slice(1, 3)
            let selected2 = JSON.stringify(selected).slice(3, 5)
            let selected3 = JSON.stringify(selected).slice(5, -1)
            let arr = []
            arr.push(selected1 + '0000')
            arr.push(selected1 + selected2 + '00')
            arr.push(selected1 + selected2 + selected3)
            this.selectAddressOptions = arr //重要，把处理后的数据重新赋值给selectOptions，让其显示
        },
        // 地址选择
        handleAddressChange () {
            let addArr = this.selectAddressOptions
            let province = CodeToText[addArr[0]]
            let city = CodeToText[addArr[1]]
            let county = CodeToText[addArr[2]]
            this.synthesizeFormData.province = province
            this.synthesizeFormData.city = city
            this.synthesizeFormData.county = county
            if(province == null && city == null && county == null) {
                this.synthesizeFormData.receiverAddress = null
            }else {
                this.synthesizeFormData.receiverAddress = province + city + county
            }
        },
        getFormDtl () {
            this.synthesizeLoading = true
            synthesizeTemporaryGetBySn({ sn: this.$route.query.sn, orgId: this.$route.query.orgId }).then(res => {
                this.synthesizeFormData = res
                if (this.showDevFunc && !this.userPermission.isSameOrg(res.orgFarId)) {
                    this.disabledElement = true
                }
                this.addressFormatShow(res)
            }).finally(() => {
                this.synthesizeLoading = false
            })
        },

    }
}
</script>

<style scoped lang="scss">
main {
    min-height: 894px;
    padding: 0 20px;
    border: 1px solid rgba(229, 229, 229, 1);
}

.list-title {
    padding: 0;
}

.detailBox {
    margin: 70px;

    .row {
        margin-bottom: 32px;
        color: rgba(51, 51, 51, 1);

        &, .col {
            display: flex;
        }

        .col {
            width: 50%;
        }

        .name {
            width: 100px;
            text-align: right;

            span {
                color: rgba(255, 95, 95, 1);
            }
        }
    }
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
::v-deep input[type=‘number’] {
    -moz-appearance: textfield !important;
}
.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}
::v-deep .summary-row,
::v-deep .summary-row td {
  height: 48px !important;
  line-height: 48px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
::v-deep .summary-row td {
    text-align:left;font-weight:bold;padding-left: 10px;font-size: 16px;
}
</style>