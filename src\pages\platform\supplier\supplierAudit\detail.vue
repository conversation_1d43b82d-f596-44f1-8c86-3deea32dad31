<template>
    <div class="e-form">
        <!-- 管理员-平台审核企业账号信息 -->
        <BillTop @cancel="handleClose"/>
        <div class="tabs warningTabs">
            <el-tabs :style="{ height: tabsContentHeight }" tab-position="left" v-model="tabsName" @tab-click="onChangeTab">
                <el-tab-pane label="企业信息" name="enterpriseInfo" :disabled="clickTabFlag" v-if="enterprise">
                </el-tab-pane>
                <el-tab-pane label="个体户信息" name="individualHouseholdInfoCon" :disabled="clickTabFlag" v-if="individualHousehold">
                </el-tab-pane>
                <el-tab-pane label="资质信息" name="qualificationInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="对公账户" name="corporateAccountInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="法人信息" name="corporationInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="管理员信息" name="adminInfo" :disabled="clickTabFlag">
                </el-tab-pane>
                <el-tab-pane label="附件资料" name="filesInfo" :disabled="clickTabFlag">
                </el-tab-pane>
<!--                <el-tab-pane :label="pcwpName" name="pcwpInfo" :disabled="clickTabFlag">-->
<!--                </el-tab-pane>-->

                <div id="tabs-content">
                  <el-form  :model="enterpriseData" label-width="200px" ref="rulesBase" class="demo-ruleForm">
                    <!--个体户信息-->
                    <div id="individualHouseholdInfoCon" class="con" v-if="individualHousehold">
                      <div class="tabs-title" id="individualHouseholdInfoCon">个体户信息</div>
                      <div style="width: 100%" class="form">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="企业名称：" prop="enterpriseName">
                              <span>{{ enterpriseData.enterpriseName }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                              <span>{{ enterpriseData.socialCreditCode }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              供方类型、纳税人类别-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="供方类型：" prop="supplierType">
                              <span>{{ changeSt(enterpriseData.supplierType) }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="纳税人类别：" prop="taxpayerType">
                              <span>{{ changeTt(enterpriseData.taxpayerType) }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--主营业务-->
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="主营业务：" prop="detailedAddress">
                              <span>{{ enterpriseData.mainBusiness }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                                <el-row>-->
                        <!-- <el-col :span="12">
                            <el-form-item label="税率：" prop="taxRate">
                                <span>{{ enterpriseData.taxRate }} %</span>
                            </el-form-item>
                        </el-col> -->
                        <!--                                </el-row>-->
                        <!-- <el-row>
                            <el-col :span="12">
                                <el-form-item label="经营者姓名：" prop="operator">
                                    <span>{{ enterpriseData.operator }}</span>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="经营场所：" prop="placeOfBusiness">
                                    <span>{{ enterpriseData.placeOfBusiness }}</span>
                                </el-form-item>
                            </el-col>
                        </el-row> -->
                        <!--法定代表人、注册资本-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="法定代表人：" prop="legalRepresentative">
                              <span>{{ enterpriseData.legalRepresentative }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="注册资本：" prop="registeredCapital">
                              <span>{{ enterpriseData.registeredCapital }}(万元)</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--注册地址、注册日期-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="注册时间：" prop="creationTime">
                              <span>{{ enterpriseData.creationTime }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="营业执照有效期：" prop="licenseTerm">
                              <span>{{ enterpriseData.licenseTerm === null ? '长期' : enterpriseData.licenseTerm }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--固定地址-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="注册地址：" prop="detailedAddress">
                              <span>{{ enterpriseData.detailedAddress }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="固定工作地址：" prop="detailedAddress">
                              <span>{{ enterpriseData.detailedAddress }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24" >
                            <el-form-item label="营业执照：" prop="businessLicense">
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24" align="center">
                            <el-image style="width: 900px; height: 500px" :src="enterpriseData.businessLicense"></el-image>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                    <!--企业信息-->
                    <div id="enterpriseInfo" class="con" v-if="enterprise">
                      <div class="tabs-title" id="enterpriseInfo">企业信息</div>
                      <div style="width: 100%" class="form">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="企业名称：" prop="enterpriseName">
                              <span>{{ enterpriseData.enterpriseName }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="统一社会信用代码：" prop="socialCreditCode">
                              <span>{{ enterpriseData.socialCreditCode }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              供方类型、纳税人类别-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="供方类型：" prop="supplierType">
                              <span>{{ changeSt(enterpriseData.supplierType) }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="纳税人类别：" prop="taxpayerType">
                              <span>{{ changeTt(enterpriseData.taxpayerType) }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              主营业务-->
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="主营业务：" prop="detailedAddress">
                              <span>{{ enterpriseData.mainBusiness }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              法定代表人、注册资本-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="法定代表人：" prop="legalRepresentative">
                              <span>{{ enterpriseData.legalRepresentative }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="注册资本：" prop="registeredCapital">
                              <span>{{ enterpriseData.registeredCapital }}(万元)</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              注册地址、注册日期-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="注册时间：" prop="creationTime">
                              <span>{{ enterpriseData.creationTime }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="营业执照有效期：" prop="licenseTerm">
                              <span>{{ enterpriseData.licenseTerm === null ? '长期' : enterpriseData.licenseTerm }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              固定地址-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="注册地址：" prop="detailedAddress">
                              <span>{{ enterpriseData.detailedAddress }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="固定工作地址：" prop="detailedAddress">
                              <span>{{ enterpriseData.detailedAddress }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              营业执照-->
                        <el-row>
                          <el-col :span="12" >
                            <el-form-item label="营业执照：" prop="businessLicense">
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="24" align="center">
                            <el-image style="width: 900px; height: 500px" :src="enterpriseData.businessLicense"></el-image>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                    <div id="qualificationInfo" class="con">
                      <div class="tabs-title" id="qualificationInfo">资质信息</div>
                      <div class="form">
                        <!--                              主要业绩表格-->
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="主要业绩">
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <div class="custom-table">
                          <el-table
                            :data="enterpriseData.epLists"
                            stripe
                            border
                            style="width: 100%">
                            <!-- 序号列 -->
                            <el-table-column label="序号" width="60" align="center" type="index">
                              <!--                                        <template slot-scope="scope" style="text-align: center">
                                                                        <el-button
                                                                            style="width: 30px;margin: 0 auto;padding: 0"
                                                                            plain="false"
                                                                            type="text"
                                                                            icon="el-icon-delete"
                                                                            @click="handleDelete(scope.$index)">
                                                                        </el-button>
                                                                      </template>-->
                            </el-table-column>
                            <!-- 项目名称列 -->
                            <el-table-column prop="projectName" label="项目名称" width="200" align="center" show-overflow-tooltip>
                              <!--                                        <template slot-scope="scope">
                                                                        <el-input v-model="scope.row.projectName" placeholder="请输入内容" :border="false" style="outline: none">{{ scope.row.projectName }}</el-input>
                                                                      </template>-->
                            </el-table-column>
                            <!-- 供应物资品类 -->
                            <el-table-column prop="supplyCategory" label="供应物资品类" width="200" align="center" show-overflow-tooltip>
                              <!--                                        <template slot-scope="scope">
                                                                        <el-input v-model="scope.row.supplyCategory" placeholder="请输入内容" style="border: none">{{ scope.row.supplyCategory }}</el-input>
                                                                      </template>-->
                            </el-table-column>
                            <!-- 合同金额（万元） -->
                            <el-table-column prop="contractAmount" label="合同金额（万元）" width="120" align="center">
                              <!--                                        <template slot-scope="scope">
                                                                        <el-input v-model="scope.row.contractAmount" placeholder="请输入内容">{{ scope.row.contractAmount }}</el-input>
                                                                      </template>-->
                            </el-table-column>
                            <!-- 供货起止时间 -->
                            <el-table-column prop="ghdate" label="供货起止时间" width="400" align="center">
                              <template slot-scope="scope">
                                <!-- <el-date-picker
                                    v-model="scope.row.ghdate"
                                    type="daterange"
                                    :value-format="dateFormat"
                                    range-separator="至"
                                    start-placeholder="请选择"
                                    end-placeholder="请选择"  style="width: 100%;">
                                </el-date-picker> -->
                                <el-text>{{scope.row.supplyStartDate}} 至 {{scope.row.supplyEndDate}}</el-text>
                              </template>
                            </el-table-column>
                            <!-- 业绩证明人 -->
                            <el-table-column prop="proofPerson" label="业绩证明人" width="100" align="center">
                              <!--                                        <template slot-scope="scope">
                                                                        <el-input v-model="scope.row.proofPerson"  style="border: none">{{ scope.row.proofPerson }}</el-input>
                                                                      </template>-->
                            </el-table-column>
                            <!-- 证明人联系电话 -->
                            <el-table-column prop="proofPhone" label="证明人联系电话" width="200" align="center">
                              <!--                                        <template slot-scope="scope">
                                                                        <el-input v-model="scope.row.proofPhone"  style="border: none">{{ scope.row.proofPhone }}</el-input>
                                                                      </template>-->
                            </el-table-column>
                          </el-table>
                        </div>
                        <!--                              质量认证-->
                        <el-row>
                          <el-col :span="24" >
                            <el-form-item label="质量认证">
                              <el-text>{{enterpriseData.certificateType}}</el-text>
                              <el-text style="margin-left: 20px;">{{enterpriseData.certificateOther}}</el-text>
                            </el-form-item>
                          </el-col>
                        </el-row>
<!--                        <el-row style="margin-left: 130px;">
                          <el-col :span="24">
                            <el-checkbox-group v-model="selectedCertificates"  style="display: flex" :disabled="true">
                              <el-checkbox label="1" >ISO9001质量体系认证</el-checkbox>
                              <el-checkbox label="2" >铁路产品CRCC认证</el-checkbox>
                              <el-checkbox label="3" >交通产品CCPC认证</el-checkbox>
                              <el-checkbox label="4" >CCC认证</el-checkbox>
                              <el-checkbox label="5" >其他质量认证</el-checkbox>
                            </el-checkbox-group>
                            <textarea v-model="enterpriseData.certificateOther" v-show="enterpriseData.certificateOther" style="width: 600px;height: 100px;resize:none;margin-top: 10px"></textarea>
                          </el-col>
                        </el-row>-->
                        <!--                              企业基本情况（企业概况、财务情况、诉讼情况）-->
                        <el-row>
                          <el-col :span="24">
                            <el-form-item label="企业基本情况">
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <div style="margin-left: 130px">
                          <div>
                            <div style="font-size: 14px">企业概况</div>
                            <textarea style="width: 95%;height: 70px;resize:none;" v-model="enterpriseData.companyProfile" disabled></textarea>
                          </div>
                          <div>
                            <div style="font-size: 14px">财务情况</div>
                            <textarea style="width: 95%;height: 70px;resize:none;" v-model="enterpriseData.financialSituation" disabled></textarea>
                          </div>
                          <div>
                            <div style="font-size: 14px">诉讼情况</div>
                            <textarea style="width: 95%;height: 70px;resize:none;" v-model="enterpriseData.litigationSituation" disabled></textarea>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="corporateAccountInfo" class="con">
                      <div class="tabs-title" id="corporateAccountInfo">对公账户</div>
                      <div class="form">
                        <!--                              对公账户信息-->
                        <!--                              开户银行、银行户名称-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="开户银行：" prop="bankName" >
                              <span>{{ enterpriseData.bankName }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="银行户名：" prop="accountName" >
                              <span>{{ enterpriseData.accountName }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              银行账号-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="银行账号：" prop="bankAccount">
                              <span>{{ enterpriseData.bankAccount }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              开票备注-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="开票备注："  prop="invoiceRemark" >
                              <span>{{ enterpriseData.invoiceRemark }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                    <div id="corporationInfo" class="con">
                      <div class="tabs-title" id="corporationInfo">法人信息</div>
                      <div class="form">
                        <!--                              法人信息-->
                        <!--                              姓名、身份证号码-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="姓名：" prop="legalPersonName" >
                              <span>{{ enterpriseData.legalPersonName }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="身份证号码：" prop="legalPersonNum">
                              <span>{{ enterpriseData.legalPersonNum }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              有效开始日期、有效结束日期-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="有效期开始日期：" prop="lpStartTime">
                              <span>{{ enterpriseData.lpStartTime }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="有效期结束日期：" prop="lpEndTime">
                              <span>{{ enterpriseData.lpEndTime == null ? '长期': enterpriseData.lpEndTime}}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              身份证人像面、国徽面-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="身份证人像面：">
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="身份证国徽面：">
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12" align="center" >
                            <el-image style="width: 360px; height: 200px" :src="enterpriseData.legalPersonFace"></el-image>
                          </el-col>
                          <el-col :span="12" align="center">
                            <el-image style="width: 360px; height: 200px" :src="enterpriseData.legalPersonNational"></el-image>
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                    <div id="adminInfo" class="con">
                      <div class="tabs-title" id="adminInfo">管理员信息</div>
                      <div class="form">
                        <!--                              管理员信息-->
                        <!--                              姓名、身份证号码-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="姓名：" prop="adminName" >
                              <span>{{ enterpriseData.adminName }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="身份证号码：" prop="adminNumber">
                              <span>{{ enterpriseData.adminNumber }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              手机号-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="手机号码：" prop="adminPhone">
                              <span>{{ enterpriseData.adminPhone }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              身份证人像面、国徽面-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="身份证人像面："  prop="cardPortraitFace" >
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="身份证国徽面："  prop="cardPortraitNationalEmblem">
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <el-row>
                          <el-col :span="12" align="center" >
                            <el-image style="width: 360px; height: 200px" :src="enterpriseData.cardPortraitFace"></el-image>
                          </el-col>
                          <el-col :span="12" align="center">
                            <el-image style="width: 360px; height: 200px" :src="enterpriseData.cardPortraitNationalEmblem"></el-image>
                          </el-col>
                        </el-row>
                        <!--有效开始日期、有效结束日期-->
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="有效期开始日期：" prop="adminPeriodStart">
                              <span>{{ enterpriseData.adminPeriodStart }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="有效期结束日期：" prop="adminPeriodEnd">
                              <span>{{ enterpriseData.adminPeriodEnd }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row>
                        <!--                              有效开始日期、有效结束日期-->
                        <!-- <el-row>
                          <el-col :span="12">
                            <el-form-item label="有效开始日期：" prop="lpStartTime">
                              <span>{{ enterpriseData.lpStartTime }}</span>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="有效结束日期：" prop="lpEndTime">
                              <span>{{ enterpriseData.lpEndTime }}</span>
                            </el-form-item>
                          </el-col>
                        </el-row> -->
                      </div>
                    </div>
                    <!--附件信息-->
                    <div id="filesInfo" class="con" v-loading="fileLoading">
                      <div class="tabs-title" id="filesInfo">入库供应商审核资料</div>
                      <div class="e-table"  style="background-color: #fff">
                        <div class="top" style="height: 50px; padding-left: 10px">
                          <div class="left">
                            <el-button type="primary" @click="batchDownloadFile">批量下载
                            </el-button>
                            <el-button type="primary" @click="batchDownloadFilePackage">批量下载并打包
                            </el-button>
                          </div>
                        </div>
                        <el-table ref="fileTableRef"
                                  border
                                  :data="fileList"
                                  class="table"
                                  @row-click="handleCurrentInventoryClick"
                                  @selection-change="selectionChangeHandle"
                        >
                          <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
                          <el-table-column label="操作" width="100">
                            <template slot-scope="scope">
                              <el-button type="primary" class="btn-greenYellow" @click="openShopDow(scope.row)">下载
                              </el-button>
                            </template>
                          </el-table-column>
                          <el-table-column label="序号" type="index" width="60"></el-table-column>
                          <el-table-column prop="name" label="附件名称"></el-table-column>
                          <el-table-column prop="category" label="附件类型"></el-table-column>
                          <el-table-column prop="startTime" label="有效期开始日期"></el-table-column>
                          <el-table-column prop="endTime" label="有效期结束日期"></el-table-column>
                        </el-table>
                      </div>
                    </div>
                    <!--入库信息-->
                    <!--                    <div id="pcwpInfo" class="con" v-loading="fileLoading">-->
                    <!--                        <div class="tabs-title" id="baseInfo">{{ pcwpName }}</div>-->
                    <!--                        <div class="e-table" style="background-color: #fff">-->
                    <!--                            <div class="top" style="height: 50px; padding-left: 10px">-->
                    <!--                                <div class="left">-->
                    <!--                                    <el-button type="primary" @click="batchDownloadFile">批量下载-->
                    <!--                                    </el-button>-->
                    <!--                                    <el-button type="primary" @click="batchDownloadFilePackage">批量下载并打包-->
                    <!--                                    </el-button>-->
                    <!--                                </div>-->
                    <!--                            </div>-->
                    <!--                            <el-table ref="fileTableRef"-->
                    <!--                                      border-->
                    <!--                                      :data="pcwpFiles"-->
                    <!--                                      class="table"-->
                    <!--                                      @row-click="handleCurrentInventoryClick"-->
                    <!--                                      @selection-change="selectionChangeHandle"-->
                    <!--                            >-->
                    <!--                                <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>-->
                    <!--                                <el-table-column label="序号" type="index" width="60"></el-table-column>-->
                    <!--                                <el-table-column prop="name" label="附件名称"></el-table-column>-->
                    <!--                                <el-table-column label="操作" width="100">-->
                    <!--                                    <template slot-scope="scope">-->
                    <!--                                        <el-button type="primary" class="btn-greenYellow" @click="openShopDow(scope.row)">下载-->
                    <!--                                        </el-button>-->
                    <!--                                    </template>-->
                    <!--                                </el-table-column>-->
                    <!--                            </el-table>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                  </el-form>
                </div>
            </el-tabs>
        </div>
        <div class="buttons">
            <el-button type="primary" @click="changeAuditState(1)" class="btn-greenYellow">审核通过</el-button>
            <el-button type="primary" @click="changeAuditState(2)" class="btn-delete">审核未通过</el-button>
            <el-button @click="handleClose">返回</el-button>
        </div>
    </div>
</template>

<script>
import JSZip from 'jszip'
// eslint-disable-next-line no-unused-vars
import FileSaver from 'file-saver'
import '@/utils/jquery.scrollTo.min'
// eslint-disable-next-line no-unused-vars
import { mapState } from 'vuex'
import $ from 'jquery'
import { throttle } from '@/utils/common'
import { changeIsSupplier } from '@/api/platform/supplier/supplierAudit'
import { previewFile } from '@/api/platform/common/file'
import { getEnterPriseFileList } from '@/api/base/file'

export default {

    data () {
        return {
            // 假设后端返回的证书类型
            certificateType: '',
            // 存储选中值的数组
            selectedCertificates: [],
            pcwpName: '',
            pcwpFiles: [],
            fileSelectList: [],
            fileLoading: false,
            fileList: [],
            individualHousehold: false,
            enterprise: false,
            alertName: '店铺',
            // 企业信息
            enterpriseData: {},
            //基本信息表单数据
            tabsName: 'enterpriseInfo',
            screenWidth: 0,
            screenHeight: 0,
            lastConHeight: 0,
            clickTabFlag: false, // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            winEvent: {},
            topHeight: 120,
            changedInfo: [],
            scrollToId: 'enterpriseInfo',
        }
    },
    created () {
        console.log('接收传参this.$route.params.row', this.$route.params.row)
        this.enterpriseData = this.$route.params.row
        this.showImageFace(this.enterpriseData.legalPersonFaceId)
        this.showImageNational(this.enterpriseData.legalPersonNationalId)
        this.pcwpName = this.enterpriseData === 0 ? '未入库供应商审核资料' : '入库供应商审核资料'
        this.getFileInfos( this.enterpriseData.enterpriseId)
        previewFile({ recordId: this.enterpriseData.businessLicenseId }).then(res => {
            let url = window.URL.createObjectURL(res)
            this.enterpriseData.businessLicense = url
        })
        previewFile({ recordId: this.enterpriseData.cardPortraitFaceId }).then(res => {
            let url = window.URL.createObjectURL(res)
            this.enterpriseData.cardPortraitFace = url
        })
        previewFile({ recordId: this.enterpriseData.cardPortraitNationalEmblemId }).then(res => {
            let url = window.URL.createObjectURL(res)
            this.enterpriseData.cardPortraitNationalEmblem = url
        })
        if(this.enterpriseData.enterpriseType === 0) {
            this.individualHousehold = true
            this.enterprise = false
        }
        if(this.enterpriseData.enterpriseType === 1) {
            this.enterprise = true
            this.individualHousehold = false
        }
    },
    mounted () {
        this.certificateType = this.enterpriseData.certificateType
        // 质量认证多选框
        const certificateMap = {
            'ISO9001质量体系认证': '1',
            '铁路产品CRCC认证': '2',
            '交通产品CCPC认证': '3',
            'CCC认证': '4',
            '其他质量认证': '5'
        }
        // 设置选中状态
        if (certificateMap[this.certificateType]) {
            this.selectedCertificates = [certificateMap[this.certificateType]]
        }
        // 获取数据
        // 获取最后一个内容区域的高度，计算底部空白
        this.getLastConHeight()
        // 保存所有tabName
        let arr = []
        if (this.enterpriseData.enterpriseType === 0) {
            arr = ['individualHouseholdInfoCon', 'qualificationInfo', 'corporateAccountInfo', 'corporationInfo', 'adminInfo', 'filesInfo']
            this.tabsName = 'individualHouseholdInfoCon'
            this.scrollToId = 'individualHouseholdInfoCon'
        } else {
            arr = ['enterpriseInfo', 'qualificationInfo', 'corporateAccountInfo', 'corporationInfo', 'adminInfo', 'filesInfo']
            this.tabsName = 'enterpriseInfo'
            this.scrollToId = 'enterpriseInfo'
        }
        this.tabArr = arr
        let $idsTop = []
        this.winEvent.onScroll = () => {
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            if (this.clickTabFlag) {
                return
            }
            this.scrollToId = ''
            if (!$idsTop[$idsTop.length - 1]) {
                $idsTop = arr.map(item => {
                    const $item = document.getElementById(item)
                    let itemTop = null
                    if ($item) {
                        itemTop = $item.offsetTop
                    }
                    return itemTop
                })
            }
            const scrollTop = $('#tabs-content')[0].scrollTop
            // 倒序查找
            let curLocal = 0
            for (let i = $idsTop.length - 1; i >= 0; i--) {
                let item = $idsTop[i]
                if (scrollTop + 1 >= item) {
                    curLocal = i
                    break
                }
            }
            // 设置对应tabName
            this.tabsName = arr[curLocal]
        }
        $('#tabs-content')[0].addEventListener('scroll', this.winEvent.onScroll)
        this.screenWidth = document.documentElement.clientWidth - this.topHeight
        this.screenHeight = document.documentElement.clientHeight - this.topHeight
        const onResize = () => {
            this.screenWidth = document.documentElement.clientWidth - this.topHeight
            this.screenHeight = document.documentElement.clientHeight - this.topHeight
            $idsTop = arr.map(item => {
                return document.getElementById(item).offsetTop
            })
        }
        this.winEvent.onResize = throttle(onResize, 500)
        window.addEventListener('resize', this.winEvent.onResize)
        if (this.$route.query.name) {
            setTimeout(() => {
                this.onChangeTab({
                    name: this.$route.query.name
                })
                this.tabsName = this.$route.query.name
            }, 200)
        }
    },
    beforeDestroy () {
        window.removeEventListener('scroll', this.winEvent.onScroll)
        window.removeEventListener('resize', this.winEvent.onResize)
    },
    computed: {
        ...mapState(['userInfo']),
        // tab内容高度
        tabsContentHeight () {
            return this.screenHeight - 70 + 'px !important'
        },
        // 填补底部空白，以使高度够滚动
    },
    watch: {
        screenHeight: {
            handler (newVal) {
                $('#tabs-content').height(newVal - 71)
            }
        },
    },
    methods: {
        changeSt (type) {
            if ( type == 1 ) {
                return '生产商'
            }else{
                return '贸易商'
            }
        },
        changeTt (type) {
            if ( type == 1 ) {
                return '一般纳税人'
            }else{
                return '小规模纳税人'
            }
        },
        async showImageFace (recordId) {
            previewFile({ recordId: recordId }).then(res => {
                this.enterpriseData.legalPersonFace = window.URL.createObjectURL(res)
            })
        },
        async showImageNational (recordId) {
            previewFile({ recordId: recordId }).then(res => {
                this.enterpriseData.legalPersonNational = window.URL.createObjectURL(res)
            })
        },
        //查询附件信息
        getFileInfos (relevanceId) {
            let params = {
                relevanceId: relevanceId,
                relevanceType: 8,
                mallType: 0
            }
            getEnterPriseFileList(params).then(res=>{
                this.fileList = res.list.filter(item => item.programaKeyTwo == null)
                //this.pcwpFiles = res.list.filter(item => item.programaKeyTwo == null)
            })
        },
        selectionChangeHandle (val) {
            this.fileSelectList = val
        },
        handleCurrentInventoryClick (row) {
            row.flag = !row.flag
            this.$refs.fileTableRef.toggleRowSelection(row, row.flag)
        },
        openShopDow (fileRow) {
            this.fileLoading = true
            previewFile({ recordId: fileRow.fileFarId }).then(res => {
                const blob = new Blob([res])
                const url = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.href = url
                a.download = fileRow.name
                a.click()
                window.URL.revokeObjectURL(url)
            }).finally(() => {
                this.fileLoading = false
            })
        },
        async batchDownloadFilePackage () {
            this.fileLoading = true
            let files = this.fileList
            if(files == null || files.length === 0) {
                this.$message.info('附件为空！')
                return
            }
            const zip = new JSZip()
            for (let i = 0; i < files.length; i++) {
                let res = await previewFile({ recordId: files[i].fileFarId })
                zip.file(files[i].name, res)
            }
            let content = await zip.generateAsync({ type: 'blob' })
            FileSaver.saveAs(content, this.enterpriseData.enterpriseName + '附件资料.zip')
            this.fileLoading = false
        },
        batchDownloadFile () {
            if(this.fileSelectList.length === 0) {
                this.$message.info('未选择文件')
                return
            }
            this.fileSelectList.forEach(t => {
                this.openShopDow(t)
            })
            this.fileSelectList = []
        },
        changeAuditState (num) {
            if (num == 2) {

                this.$prompt('审核不通过原因', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error',
                    inputType: 'textarea',
                    inputPlaceholder: '请输入审核不通过原因',
                    inputPattern: /^.+$/,
                    inputErrorMessage: '请输入审核不通过原因'
                }).then(({ value }) => {
                    let params = { enterpriseId: this.enterpriseData.enterpriseId, isSupplier: 0, isNoSupplierAudit: 1, auditFailReason: value }
                    if (this.enterpriseData.zcstate == 0) {
                        params.zcstate = 0
                    }else{
                        params.isFileModify = 0
                    }
                    changeIsSupplier([params]).then(res => {
                        this.handleClose()
                        this.message(res)
                    })
                })

                // this.clientPop('info', '您确定不通过审核吗！', async () => {
                //
                // })
            }else {
                // 通过
                if (this.enterpriseData.taxRate != null) {
                    let params = { enterpriseId: this.enterpriseData.enterpriseId, isSupplier: 2 }
                    if (this.enterpriseData.zcstate == 0) {
                        params.zcstate = 1
                    }else{
                        params.isFileModify = 0
                    }
                    changeIsSupplier([params]).then(res => {
                        this.handleClose()
                        this.message(res)
                    })
                }else {
                    this.$message.error('税率不能为空')
                }
            }
        },
        //取消
        handleClose () {
            this.$router.replace('/platform/supplier/supplierAudit')
        },
        onChangeTab (e) {
            if ( this.scrollToId === e.name ) {
                return ''
            }
            this.scrollToId = e.name
            const height = document.querySelector('#' + e.name)
            try {
                $('#tabs-content').scrollTo(height.offsetTop, 500)
            } catch (err) {
                console.log(err)
            }
            // 如果手动点击时，则不做滚动事件逻辑，只有滚动屏幕时才走滚动事件逻辑
            this.clickTabFlag = true
            // 动画结束后，恢复状态
            setTimeout(() => {
                this.clickTabFlag = false
            }, 600)
        },
        // 获取最后一个内容区域的高度，计算底部空白
        getLastConHeight () {
            let si = setInterval(() => {
                // 因为dom异步加载，通过轮询找到渲染后的dom，获取高度
                if (document.getElementById('terminationLog')) {
                    this.lastConHeight = document.getElementById('terminationLog').offsetHeight
                    clearInterval(si)
                    si = null
                }
            }, 100)
        },
        // 消息提示
        message (res) {
            if(res.code === 200) {
                this.$message({
                    message: res.message,
                    type: 'success'
                })
            }
        },
    }
}
</script>

<style lang='scss' scoped>
.warningTabs {
    padding-top: 70px;
}
.e-table {
    min-height: auto;
    background: #fff;
}

.upload {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    text-align: center;
}

.upload-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

/deep/.el-input--suffix .el-input__inner {
    padding-right: 5px;
}

/deep/ .el-tabs__content {
    // overflow: hidden;
    &::-webkit-scrollbar {width: 0;}
}
//表格
.custom-table {
  width: 85%;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  margin-left: 130px;
  margin-bottom: 30px;
}
</style>
