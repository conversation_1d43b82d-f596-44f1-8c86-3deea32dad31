<template>
  <div class="page" v-loading="menuLoading">
    <div class="full" style="padding-right: 11px;">
      <div class="menu full" v-show="showMenu">
        <div class="title">
          <img class="logo" src="../../assets/images/supplier_performance_platform.png" alt=""/>
        </div>
        <el-menu :default-active="defaultActive" class="el-menu-vertical-demo" mode="vertical"
                 background-color="#ffffff00"
                 text-color="#fff" active-text-color="#FFD41C" :unique-opened="true" menu-trigger="click"
                 :router="true">
          <template v-for="item in menu">
            <template v-if="item.show">
              <!-- 包含子菜单1 -->
              <template v-if="item.children">
                <el-submenu
                  :key="item.menuId"
                  :index="item.menuId"
                  @click="changePath(1, item.menuName)"
                >
                  <template slot="title">
                    <div class="bar"></div>
                    <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                    <span>{{ item.menuName }}</span>
                  </template>
                  <div class="menu-item-box">
                    <el-menu-item
                      v-show="subItem.show"
                      v-for="subItem in item.children"
                      @click="changePath(2, subItem.menuName, item.menuName)"
                      :key="subItem.menuId"
                      :index="subItem.menuId"
                      :route="subItem.route"
                    >
                      <template slot="title">
                        <img :src="images.dot" alt=""/>
                        <span>{{ subItem.menuName }}</span>
                      </template>
                    </el-menu-item>
                  </div>
                </el-submenu>
              </template>
              <!-- 不包含子菜单 -->
              <template v-else>
                <el-menu-item
                  :key="item.menuId"
                  :index="item.menuId"
                  :route="item.route"
                  @click="changePath(0, item.menuName)"
                >
                  <div class="bar"></div>
                  <img :src="images.gear2" style="margin-right: 10px" alt=""/>
                  <span slot="title">{{ item.menuName }}</span>
                </el-menu-item>
              </template>
            </template>
          </template>
        </el-menu>
      </div>
      <div id="fold-btn" @click="showMenu = !showMenu"></div>
    </div>
    <div class="table-box">
      <div class="history">
        <top-btn-bar :org-name="userInfo.orgName"></top-btn-bar>
      </div>
      <div :style="{ maxHeight: contentHeight+'px', height: '100%', background: '#eff2f6' }">
        <div class="router-box">
          <top-step :stepInfo="steps" v-show="showSteps" />
          <keep-alive>
            <router-view :style="{ maxHeight: contentHeight-70+'px' }" :key="$route.fullPath" v-if="$route.meta.keepAlive"></router-view>
          </keep-alive>
          <router-view :style="{ maxHeight: contentHeight-70+'px' }" :key="$route.fullPath" v-if="!$route.meta.keepAlive"></router-view>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import $ from 'jquery'
import topStep from '../../components/topstep/topstep'
import topBtnBar from '../../components/topSuppliesButtonBar'
import foldBtn from '../../assets/menu_close.png'
import openBtn from '../../assets/menu_open.png'
import { getShopStateByUserId, isTwoSupper, getIsShowFee } from '@/api/frontStage/mallWebHeader'
import { mapState, mapActions } from 'vuex'
import { debounce } from '@/utils/common'
import { getParamsByCode } from '@/api/platform/system/systemParam'

export default {
    components: { topStep, topBtnBar },
    watch: {
        showMenu: {
            handler (newVal) {
                let btnImg = newVal ? `url(${foldBtn})` : `url(${openBtn})`
                $('#fold-btn').css('background-image', btnImg)
            }
        },
        steps (newVal) {
            this.changeSteps(newVal)
        },
        $route: {
            handler () {
                this.setDefaultActive()
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        ...mapState([ 'userInfo']),
        contentHeight () {
            return this.screenHeight - 50
        },
    },
    data () {
        return {
            defaultActive: '1',
            menuLoading: false,
            isShowFee: null,
            isBidAddShow: null,
            showMenu: true,
            showSteps: false,
            images: {
                gearActive: require('@/assets/images/zbgl2.png'),
                gear: require('@/assets/images/zbgl.png'),
                gear2: require('@/assets/images/ershou.png'),
                gear2Active: require('@/assets/images/zbgl.png'),
                rental: require('@/assets/images/zlgl.png'),
                rentalActive: require('@/assets/images/zlgl2.png'),
                repair: require('@/assets/images/wxbyfw.png'),
                repairActive: require('@/assets/images/wxbyfw2.png'),
                order: require('@/assets/images/ddgl.png'),
                orderActive: require('@/assets/images/ddgl2.png'),
                backTitle: require('@/assets/images/logodpgl.png'),
                dot: require('@/assets/images/dot219.png'),
                arrow: require('@/assets/images/tragle829.png')
            },
            steps: [
                { description: '', },
                { description: '', }
            ],
            submenuName: '',
            // 校验当前登录供应商是否是物资分公司
            materialsBranch: false,
            // 导航菜单数据
            // authList表示属于什么，0：供应商 1：店铺通过审核(未停用)，2：pcwp入库供应商
            menu: [
                {
                    menuName: '消息管理',
                    menuId: '1',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '收件箱', menuId: '1-1', route: { path: '/supplierSys/product/inBox', } },
                    ],
                },
                {
                    menuName: '内容管理',
                    menuId: '2',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '评价管理', menuId: '2-1', route: { path: '/supplierSys/comment/commentManage', } },
                        { show: true, menuName: '供应商汇总评价', menuId: '2-2', route: { path: '/supplierSys/comment/supplierAggregateComment', } },
                    ],
                },
                // {
                //     menuName: '招标提醒',
                //     menuId: '2',
                //     authList: [0, 2],
                //     show: false,
                //     children: [
                //         { show: true, menuName: '我的提醒', menuId: '2-1', route: { path: '/supplierSys/myReminder/reminder', } },
                //     ],
                // },
                // {
                //     menuName: '投标管理',
                //     menuId: '4',
                //     authList: [0, 2],
                //     show: false,
                //     children: [
                //         { show: true, menuName: '参与的投标', menuId: '4-1', route: { path: '/supplierSys/bidManage/attendBiding', } },
                //         { show: true, menuName: '中标的投标', menuId: '4-2', route: { path: '/supplierSys/bidManage/bidManage', } },
                //     ],
                // },
                {
                    menuName: '商品管理',
                    menuId: '5',
                    authList: [0],
                    show: false,
                    children: [
                        // 如果打开请注意菜单控制
                        // { show: true, menuName: '已确认零星商品', menuId: '5-1', route: { path: '/supplierSys/product/materialSupplierYesAffirm', } },
                        { show: true, menuName: '零星采购商品', menuId: '5-2', route: { path: '/supplierSys/product/materialSupplierNotSubmit', } },
                        // { show: true, menuName: '未确认的零星商品', menuId: '5-3', route: { path: '/supplierSys/product/materialSupplierNotAffirm', } },
                        // { show: true && this.showDevFunc, menuName: '已确认的临购商品', menuId: '5-4', route: { path: '/supplierSys/product/lcMaterialSupplierYesAffirm', } },
                        { show: true, menuName: '大宗临购商品', menuId: '5-5', route: { path: '/supplierSys/product/lcMaterialSupplierNotSubmit', } },
                        // { show: true && this.showDevFunc, menuName: '未确认的临购商品', menuId: '5-6', route: { path: '/supplierSys/product/lcMaterialSupplierNotAffirm', } },
                        { show: true, menuName: '周转材料商品', menuId: '5-6', route: { path: '/supplierSys/product/turnMaterialSupplierNotSubmit', } },
                    ],
                },
                {
                    menuName: '店铺商品管理',
                    menuId: '6',
                    authList: [0, 1],
                    show: false,
                    children: [
                        // 废弃
                        // { show: true, menuName: '出售中零星商品', menuId: '6-1', route: { path: '/supplierSys/product/materialManage', } },
                        // { show: true, menuName: '审核的零星商品', menuId: '6-3', route: { path: '/supplierSys/product/materialCheck', } },
                        { show: true, menuName: '零星采购商品', menuId: '6-2', route: { path: '/supplierSys/product/materialWarehouse', } },
                        { show: true, menuName: '待确认的零星采购商品', menuId: '6-4', route: { path: '/supplierSys/product/materialSupplierAffirm', } },
                        { show: true, menuName: '大宗临购商品', menuId: '6-5', route: { path: '/supplierSys/product/lcMaterialWarehouse', } },
                        { show: true, menuName: '待确认的大宗临购商品', menuId: '6-6', route: { path: '/supplierSys/product/lcMaterialSupplierAffirm', } },
                        { show: true, menuName: '周转材料商品', menuId: '6-8', route: { path: '/supplierSys/product/turnMaterialWarehouse', } },
                        { show: true, menuName: '待确认的周转材料商品', menuId: '6-9', route: { path: '/supplierSys/product/turnMaterialSupplierAffirm', } },
                        { show: true, menuName: '一键铺货', menuId: '6-7', route: { path: '/supplierSys/product/oneClickStockUp', } },
                    ],
                },
                // {
                //     menuName: '大宗临购商品管理',
                //     menuId: '17',
                //     authList: [0],
                //     show: false,
                //     children: [
                //
                //     ],
                // },
                // {
                //     menuName: '店铺大宗临购商品管理',
                //     menuId: '18',
                //     show: false,
                //     authList: [0, 1],
                //     children: [
                //         // { show: true && this.showDevFunc, menuName: '出售中临购商品', menuId: '18-1', route: { path: '/supplierSys/product/lcMaterialManage', } },
                //         // { show: true && this.showDevFunc, menuName: '审核的临购商品', menuId: '18-3', route: { path: '/supplierSys/product/lcMaterialCheck', } },
                //     ],
                // },
                {
                    menuName: '二级订单管理',
                    menuId: '7',
                    authList: [0],
                    show: false,
                    children: [
                        // { show: true, menuName: '待确认零星采购订单', menuId: '7-1', route: { path: '/supplierSys/order/searchOrder/twoOrder', } },
                        { show: true, menuName: '零星采购订单', menuId: '7-2', route: { path: '/supplierSys/order/searchOrder/twoOrder/okIndex/0', } },
                        { show: true, menuName: '零星采购发货单', menuId: '7-3', route: { path: '/supplierSys/order/searchOrder/twoOrder/shiped', } },
                        { show: true, menuName: '零星采购对账单对账', menuId: '7-9', route: { path: '/supplierSys/sheet/twoSupplierTwoSheet', } },
                        { show: false, menuName: '大宗月供计划', menuId: '7-14', route: { path: '/supplierSys/order/searchOrder/secondLevelMonthPlan', } },
                        // { show: true && this.showDevFunc, menuName: '待确认大宗月供订单', menuId: '7-6', route: { path: '/supplierSys/order/searchOrder/contractPlanTwoOrder', } },
                        { show: false, menuName: '大宗月供订单', menuId: '7-7', route: { path: '/supplierSys/order/searchOrder/contractPlanTwoOKOrder', } },
                        { show: false, menuName: '大宗月供发货单', menuId: '7-11', route: { path: '/supplierSys/order/searchOrder/contractPlanTwoOKOrder/shipped', } },
                        { show: false, menuName: '大宗月供对账单对账', menuId: '7-13', route: { path: '/supplierSys/sheet/dzMonth/supplierTwoSheet', } },
                        // { show: true && this.showDevFunc, menuName: '待确认大宗临购订单', menuId: '7-4', route: { path: '/supplierSys/order/blockOrder/twoOrder', } },
                        { show: true, menuName: '大宗临购订单', menuId: '7-5', route: { path: '/supplierSys/order/searchOrder/twoOrder/okIndex/1', } },
                        { show: true, menuName: '大宗临购发货单', menuId: '7-8', route: { path: '/supplierSys/order/blockOrder/twoOrder/shipped', } },
                        { show: true, menuName: '大宗临购对账单对账', menuId: '7-10', route: { path: '/supplierSys/sheet/blockLinCai/supplierTwoSheet', } },
                        // 周转材料
                        { show: true, menuName: '周转材料订单', menuId: '7-15', route: { path: '/supplierSys/order/searchOrder/twoOrder/okIndex/2', } },
                        { show: true, menuName: '周转材料发货单', menuId: '7-16', route: { path: '/supplierSys/order/turnoverMaterials/twoOrder/shipped', } },
                        { show: true, menuName: '周转材料对账单对账', menuId: '7-17', route: { path: '/supplierSys/sheet/turnoverMaterials/supplierTwoSheet', } },
                    ],
                },
                {
                    menuName: '二级订单退货',
                    menuId: '8',
                    authList: [0],
                    show: false,
                    children: [
                        // { show: true, menuName: '零星采购退货申请', menuId: '8-1', route: { path: '/supplierSys/returnGoods/twoApply', } },
                        { show: true, menuName: '零星采购退货', menuId: '8-2', route: { path: '/supplierSys/returnGoods/twoRecord', } },
                        // { show: true, menuName: '大宗临购退货申请', menuId: '8-3', route: { path: '/supplierSys/returnGoods/twoApply/blockIndex', } },
                        { show: true, menuName: '大宗临购退货', menuId: '8-4', route: { path: '/supplierSys/returnGoods/block/twoRecord', } },
                        { show: false, menuName: '大宗月供退货', menuId: '8-5', route: { path: '/supplierSys/returnGoods/second/bulk/index', } }
                    ],
                },
                {
                    menuName: '订单管理',
                    menuId: '9',
                    authList: [0],
                    show: false,
                    children: [
                        //供应商
                        { show: true, menuName: '零星采购订单', menuId: '9-1', route: { path: '/supplierSys/order/smallShipTwo/searchOrder', } },
                        { show: true, menuName: '零星采购发货单', menuId: '9-2', route: { path: '/supplierSys/order/shiped/searchOrder', } },
                        //物资公司
                        { show: true, menuName: '零星采购多供方订单', menuId: '9-3', route: { path: '/supplierSys/order/searchOrder/towIndex',  } },
                        { show: true, menuName: '零星采购多供方发货单', menuId: '9-9', route: { path: '/supplierSys/order/searchOrder/twoOrder/twoship', } },
                        { show: false, menuName: '大宗月供应计划', menuId: '9-5', route: { path: '/supplierSys/order/searchOrder/monthPlan', } },
                        { show: false, menuName: '大宗月供订单', menuId: '9-6', route: { path: '/supplierSys/order/searchOrder/monthPlanOrder',  } },
                        { show: false, menuName: '大宗月供发货单', menuId: '9-7', route: { path: '/supplierSys/order/searchOrder/monthPlanShipOrder',  } },
                        // TODO 待菜单控制
                        { show: false, menuName: '大宗月供多供方订单', menuId: '9-8', route: { path: '/supplierSys/order/searchOrder/monthPlanTwoOrder',  } },
                        { show: false, menuName: '大宗月供多供方发货单', menuId: '9-13', route: { path: '/supplierSys/order/searchOrder/dzMonthTowIndex',  } },
                        //物资分公司
                        { show: true, menuName: '大宗临购多供方订单', menuId: '9-10', route: { path: '/supplierSys/order/blockOrder/searchOrder', } },
                        { show: true, menuName: '大宗临购多供方发货单', menuId: '9-11', route: { path: '/supplierSys/order/blockOrder/twoOrder/twoShip', } },
                        // //供应商
                        { show: true, menuName: '大宗临购清单', menuId: '9-12', route: { path: '/supplierSys/synthesizeTemporary/noAffirm', } },
                        // { show: true && this.showDevFunc, menuName: '已确认的大宗临购清单', menuId: '9-13', route: { path: '/supplierSys/synthesizeTemporary/yesAffirm', } },
                        // { show: true && this.showDevFunc, menuName: '审核大宗临购清单', menuId: '9-14', route: { path: '/supplierSys/synthesizeTemporary/audit', } },
                        { show: true, menuName: '周转材料多供方订单', menuId: '9-14', route: { path: '/supplierSys/order/turnoverMaterials/searchOrder', } },
                        { show: true, menuName: '周转材料多供方发货单', menuId: '9-15', route: { path: '/supplierSys/order/turnoverMaterials/twoOrder/twoShip', } },
                        // { show: true, menuName: '周转材料清单', menuId: '9-16', route: { path: '/supplierSys/synthesizeTemporary/noAffirm', } },
                        { show: true, menuName: '工单管理', menuId: '9-16', route: { path: '/supplierSys/order/work/index', } },
                    ],
                },
                // {
                //     menuName: '服务及费用管理',
                //     menuId: '20',
                //     authList: [0],
                //     show: true,
                //     children: [
                //         { show: true, menuName: '年费缴纳记录', menuId: '20-23', route: { path: '/supplierSys/fee/year', } },
                //         // { show: true, menuName: '年度服务费缴费', menuId: '20-1', route: { path: '/supplierSys/fee/yearPayRecord', } },
                //         { show: true, menuName: '缴费管理', menuId: '20-3', route: { path: '/supplierSys/fee/payRecordManage', } },
                //         { show: true, menuName: '费用查询', menuId: '20-4', route: { path: '/supplierSys/fee/freeDtlAll', } },
                //         { show: true, menuName: '交易统计', menuId: '20-5', route: { path: '/supplierSys/feeDtl/dealPayRecord', } },
                //         { show: true, menuName: '申请电子招标服务', menuId: '20-6', route: { path: '/index?bid=true', } },
                //         { show: true, menuName: '服务交易费记录', menuId: '20-22', route: { path: '/supplierSys/feeDtl/dealPayRecord', } },
                //     ],
                // },
                {
                    menuName: '竞价管理',
                    menuId: '10',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '我参与的竞价', menuId: '10-1', route: { path: '/supplierSys/bidManage/myBidding', } },
                        { show: true, menuName: '发布竞价', menuId: '10-2', route: { path: '/supplierSys/bidManage/bidingList', } },
                        { show: true, menuName: '竞价记录', menuId: '10-3', route: { path: '/supplierSys/bidManage/bidingRecord', } },
                    ],
                },
                {
                    menuName: '对账',
                    menuId: '11',
                    authList: [0],
                    show: false,
                    children: [
                        { show: true, menuName: '对账单', menuId: '11-1', route: { path: '/supplierSys/sheet/sheet' } },
                        { show: true, menuName: '二级供应商零星对账单', menuId: '11-2', route: { path: '/supplierSys/sheet/twoSheet' } },
                        { show: false, menuName: '二级供应商大宗月供对账单', menuId: '11-5', route: { path: '/supplierSys/sheet/dzMonth/twoSupplier/twoSheet' } },
                        { show: false, menuName: '大宗临购对账单', menuId: '11-4', route: { path: '/supplierSys/sheet/synthesizeTemporary' } },
                        { show: true, menuName: '二级供应商大宗临购对账单', menuId: '11-3', route: { path: '/supplierSys/sheet/blockLinCai/twoSheet' } },
                        { show: true, menuName: '二级供应商周转材料对账单', menuId: '11-6', route: { path: '/supplierSys/sheet/turnoverMaterials/twoSheet' } },
                    ]
                },
                {
                    menuName: '退货管理',
                    menuId: '12',
                    authList: [0],
                    show: false,
                    children: [
                        // { show: true, menuName: '零星采购退货', menuId: '12-1', route: { path: '/supplierSys/returnGoods/invoice', } },
                        { show: true, menuName: '零星采购退货', menuId: '12-2', route: { path: '/supplierSys/returnGoods/record', } },
                        // { show: true, menuName: '大宗临购多供方退货申请', menuId: '12-3', route: { path: '/supplierSys/returnGoods/invoice/blockIndex', } },
                        { show: true, menuName: '大宗临购多供方退货', menuId: '12-4', route: { path: '/supplierSys/returnGoods/record/block', } },
                        { show: false, menuName: '大宗月供多供方退货', menuId: '12-6', route: { path: '/supplierSys/returnGoods/bulk/index', } },
                        { show: false, menuName: '大宗月供退货', menuId: '12-7', route: { path: '/supplierSys/returnGoods/dzMonthRecord', } },
                        { show: true, menuName: '周转材料多供方退货', menuId: '12-8', route: { path: '/supplierSys/returnGoods/zzDRecord', } },
                        { show: true, menuName: '周转材料退货', menuId: '12-9', route: { path: '/supplierSys/returnGoods/zzRecord', } },
                        // { show: true, menuName: '大宗月供多供方退货', menuId: '12-5', route: { path: '/supplierSys/returnGoods/bulk/index', } },
                    ],
                },
                {
                    menuName: '发票管理',
                    menuId: '13',
                    authList: [0, 1],
                    show: false && this.showDevFunc,
                    children: [
                        { show: true && this.showDevFunc, menuName: '我的发票', menuId: '13-1', route: { path: '/supplierSys/invoice/invoice', } },
                        { show: true && this.showDevFunc, menuName: '我的二级发票', menuId: '13-2', route: { path: '/supplierSys/invoice/twoIndex', } },
                        { show: true && this.showDevFunc, menuName: '发票抬头', menuId: '13-3', route: { path: '/supplierApply/invoiceRise', } },

                    ],
                },
                {
                    menuName: '店铺管理',
                    menuId: '14',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '店铺信息', menuId: '14-1', route: { path: '/supplierSys/shop/shopInfoManage', } },
                        { show: true, menuName: '供方管理', menuId: '14-2', route: { path: '/supplierSys/shop/selectSupplier', } },
                        { show: true, menuName: '年费缴纳记录', menuId: '20-1', route: { path: '/supplierSys/fee/yearPayRecord', } },
                        { show: true, menuName: '交易服务费缴费', menuId: '20-2', route: { path: '/supplierSys/fee/dealPayRecord', } },
                    ],
                },
                {
                    menuName: '统计分析',
                    menuId: '15',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '店铺订单统计', menuId: '15-1', route: { path: '/supplierSys/analysis/order', } },
                        { show: true, menuName: '店铺订单项统计', menuId: '15-2', route: { path: '/supplierSys/analysis/orderItem', } },
                        { show: true, menuName: '商品统计', menuId: '15-3', route: { path: '/supplierSys/analysis/product', } }
                    ],
                },
                {
                    menuName: '报表管理',
                    menuId: '16',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '上架商品报表', menuId: '16-1', route: { path: '/supplierSys/analysis/productReportForms', } },
                        { show: true, menuName: '商品交易量报表', menuId: '16-2', route: { path: '/supplierSys/analysis/tradingVolumeReport', } },
                        { show: true, menuName: '物资结算报表', menuId: '16-3', route: { path: '/supplierSys/analysis/productSheet', } },
                        // { show: true, menuName: '零星采购结算报表', menuId: '16-4', route: { path: '/supplierSys/analysis/orderStatement', } }
                        { show: true, menuName: '运营统计报表', menuId: '16-5', route: { path: '/supplierSys/analysis/operationStatistics', } },
                        { show: true, menuName: '物资对账统计台账', menuId: '16-6', route: { path: '/supplierSys/analysis/reconciliationStatistics', } },
                    ],
                },
                {
                    menuName: '库存管理',
                    menuId: '17',
                    authList: [0, 1],
                    show: false,
                    children: [
                        { show: true, menuName: '仓库管理', menuId: '17-1', route: { path: '/supplierSys/analysis/inventoryManagement', } },
                        { show: true, menuName: '出入库记录', menuId: '17-2', route: { path: '/supplierSys/analysis/inOnBoundRecord', } },
                        { show: true, menuName: '入库管理', menuId: '17-3', route: { path: '/supplierSys/analysis/inboundManagement', } },
                        { show: true, menuName: '出库管理', menuId: '17-4', route: { path: '/supplierSys/analysis/onBoundManagement', } }
                    ],
                },
            ],
            screenHeight: 0,
        }
    },
    methods: {
        ...mapActions(['changeSteps']),
        setDefaultActive () {
            // let rName = this.$route.name
            //Detail
            let path = this.$route.path
            this.menu.forEach(item => {
                if(item.route && item.route.path === path) {
                    this.steps = [{ description: item.menuName }]
                    this.defaultActive = item.menuId
                }
                if(!item.children) return
                item.children.forEach(subItem => {
                    // if(subItem.route.path !== path) return
                    if(subItem.route.path == path.replace('Detail', '')) {
                        this.defaultActive = subItem.menuId
                        this.submenuName = item.menuName
                        this.steps[0].description = item.menuName
                        this.steps[1].description = subItem.menuName
                    }
                    // 处理菜单高亮
                    if(path.indexOf('myLgBiddingDetail') > -1 || path.indexOf('myBiddingDetail') > -1) {
                        // 我参与的竞价
                        if (item.menuName === '竞价管理' && subItem.menuName === '我参与的竞价') {
                            this.defaultActive = subItem.menuId
                            this.submenuName = subItem.menuName
                            this.steps[0].description = item.menuName
                            this.steps[1].description = subItem.menuName
                        }
                    }

                })
            })
        },
        getParamsByCodeM () {
            getParamsByCode({ code: 'materialUnit', size: 100 }).then(res => {
                if(res.length !== 0) {
                    let materialUnit = res.map(item => ({ label: item.keyValue, value: item.keyValue2 }))
                    this.$store.commit('setMaterialUnit', materialUnit)
                }
            })
        },
        // 修改路径显示
        changePath (num, name) {
            !this.showSteps ? this.showSteps = true : null
            if(this.steps.length === 1) {
                num === 0 ? this.steps[0].description = name : this.steps.push({ description: name })
            }else{
                if(num === 0) {
                    this.steps.pop()
                    this.steps[0].description = name
                }else{
                    this.steps[0].description = this.submenuName
                    this.steps[1].description = name
                }
            }
            this.changeSteps(this.steps)
        },
        getScreenInfo () {
            this.screenHeight = document.documentElement.clientHeight || document.body.clientHeight

        },
    },
    async created () {
        this.menuLoading = true
        // 获取计量单位
        this.getParamsByCodeM()
        // 获取是否二级供方
        let twoSupplierAuth = await isTwoSupper()
        if(this.materialFee) {
            let res = await getIsShowFee()
            this.isShowFee = res.isShowFee
            this.isBidAddShow = res.isBidAddShow
        }
        // 店铺是否通过审核
        let { auditStatus, state } = await getShopStateByUserId()
        for (let i = 0; i < this.menu.length; i++) {
            let menuItem = this.menu[i]
            // 是供应商并且入了pcwp库
            let isSupplierAndPcwp = [0, 2]
            let isSupplierAndPcwpB = isSupplierAndPcwp.every(e => menuItem.authList.includes(e))
            if(isSupplierAndPcwpB) {
                if(this.userInfo.isPcwp != null && this.userInfo.isPcwp == 1) {
                    menuItem.show = true
                }else {
                    if(menuItem.menuName === '合同管理') {
                        if(this.userInfo.isInterior === 1) {
                            menuItem.show = true
                            continue
                        }
                    }
                    menuItem.show = false
                }
                // 跳过循环
                continue
            }
            // 店铺显示
            let isShopArr = [0, 1]
            let isShop = isShopArr.every(e => menuItem.authList.includes(e))
            if(isShop) {
                // 店铺停用后仍然需要消息管理
                if (menuItem.menuName.indexOf('消息管理') > -1) {
                    menuItem.show = true
                    continue
                }
                if(auditStatus === 1 && this.userInfo.shopId != null && state === 1) {
                    menuItem.show = true
                    // 自定义菜单显示
                    if(this.userInfo.shopClass != 2) {
                        if (menuItem.menuName === '店铺管理') {
                            // 隐藏供方管理
                            menuItem.children[1].show = false
                        }
                        if (menuItem.menuName === '店铺商品管理') {
                            // 隐藏待确认的商品
                            menuItem.children[1].show = false
                            menuItem.children[3].show = false
                            menuItem.children[5].show = false
                        }
                        // // 隐藏大宗临购待确认的商品
                        // if(menuItem.menuName === '店铺大宗临购商品管理') {
                        //     // 隐藏待确认的商品
                        //     menuItem.children[1].show = false
                        // }
                    }
                    if (menuItem.menuName === '店铺商品管理') {
                        let orgName = this.userInfo.enterpriseName
                        // if(orgName == null || orgName !== '四川路桥建设集团股份有限公司物资分公司') {
                        if ((orgName == null || (orgName !== '四川路桥建设集团股份有限公司物资分公司' && orgName !== '四川路桥建设集团物资有限责任公司'))) {
                            menuItem.children[2].show = false
                        }
                    }
                } else {
                    menuItem.show = false
                }
                // 如果是店铺直接跳过循环
                continue
            }
            // 供应商显示
            let isSupplierArr = [0]
            let isSup = isSupplierArr.every(e => menuItem.authList.includes(e))
            if (isSup) {
                menuItem.show = true
                // 自定义菜单显示
                if (menuItem.menuName === '商品管理' || menuItem.menuName === '二级订单管理' || menuItem.menuName === '二级订单退货' || menuItem.menuName === '大宗临购商品管理') {
                    // 如果不是二级供方隐藏菜单
                    if (twoSupplierAuth.code == 200 && twoSupplierAuth.data == null) {
                        menuItem.show = false
                    } else {
                        if (menuItem.menuName === '商品管理') {
                            if (twoSupplierAuth.listPermissions != 1 || twoSupplierAuth.permissionsLowValue != 1) {
                                // menuItem.children[0].show = false
                                // menuItem.children[1].show = false
                                // menuItem.children[2].show = false
                                menuItem.children[0].show = false
                            }
                            if (twoSupplierAuth.listPermissions != 1 || twoSupplierAuth.permissionsCommodities != 1) {
                                // menuItem.children[3].show = false
                                // menuItem.children[4].show = false
                                // menuItem.children[5].show = false
                                menuItem.children[1].show = false
                            }
                            if (twoSupplierAuth.listPermissions != 1 && twoSupplierAuth.listPermissions != 1 && twoSupplierAuth.permissionsLowValue != 1 && twoSupplierAuth.permissionsCommodities != 1) {
                                menuItem.show = false
                            }
                        }
                    }
                }
                // if (menuItem.menuName == '服务及费用管理') {
                //     if (this.isShowFee == 0) {
                //         menuItem.show = false
                //     }
                //     if (this.isBidAddShow == 0) {
                //         menuItem.children[menuItem.children.length - 1].show = false
                //     }
                // }
                if (menuItem.menuName == '内容管理') {
                    if (!(twoSupplierAuth.code == 200 && twoSupplierAuth.data == null)) {
                        menuItem.children[1].show = false
                    }
                }
                if (menuItem.menuName == '对账') {
                    if (this.userInfo.isBusiness == null || this.userInfo.isBusiness != 1) {
                        menuItem.children[1].show = false
                        menuItem.children[4].show = false
                        menuItem.children[2].show = false
                        menuItem.children[3].show = false
                    }
                }
                if (menuItem.menuName == '退货管理') {
                    if (this.userInfo.isBusiness == null || this.userInfo.isBusiness != 1) {
                        // menuItem.children[2].show = false
                        // menuItem.children[3].show = false
                        // menuItem.children[4].show = false
                        menuItem.children[1].show = false
                        menuItem.children[2].show = false
                        // menuItem.children[3].show = false
                    } else {
                        // menuItem.children[5].show = false
                        menuItem.children[0].menuName = '零星采购多供方退货'
                        menuItem.children[3].show = false
                    }
                }
                if (menuItem.menuName == '竞价管理') {
                    if (this.userInfo.enterpriseName != '四川路桥建设集团股份有限公司物资分公司' && this.userInfo.enterpriseName != '四川路桥建设集团物资有限责任公司') {
                        menuItem.children[1].show = false
                    }
                }
                if (menuItem.menuName === '订单管理') {
                    // if(this.userInfo.enterpriseName !== '四川路桥建设集团股份有限公司物资分公司') {
                    //     menuItem.children[10].show = false
                    // }
                    if (auditStatus === 1 && this.userInfo.shopId != null && state === 1) {
                        if (this.userInfo.shopClass != 2) {
                            menuItem.children[2].show = false
                            menuItem.children[3].show = false
                            menuItem.children[7].show = false
                            menuItem.children[8].show = false
                            menuItem.children[9].show = false
                            menuItem.children[10].show = false
                            menuItem.children[11].show = false
                            menuItem.children[12].show = false
                            menuItem.children[13].show = false
                            menuItem.children[14].show = false
                            // menuItem.children[menuItem.children.length - 3].show = false
                            // menuItem.children[menuItem.children.length - 4].show = false
                            // menuItem.children[9].show = false
                        }
                        if (this.userInfo.shopClass == 2) {
                            // 隐藏普通供应商
                            menuItem.children[0].show = false
                            menuItem.children[1].show = false
                            menuItem.children[5].show = false
                            menuItem.children[6].show = false

                        }
                        if (this.userInfo.shopClass == null) {
                            // 隐藏多供方订单信息
                            menuItem.children[2].show = false
                            menuItem.children[3].show = false
                            menuItem.children[7].show = false
                            menuItem.children[9].show = false
                            menuItem.children[14].show = false
                            // menuItem.children[9].show = false
                        }
                    } else {
                        menuItem.children[0].show = false
                        menuItem.children[1].show = false
                        menuItem.children[2].show = false
                        menuItem.children[3].show = false
                        // menuItem.children[7].show = false
                    }
                }
            } else {
                menuItem.show = false
            }
        }
        this.menuLoading = false
    },
    mounted () {
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
    }
}
</script>
<style scoped lang="scss">
@import '../../assets/css/backStage.css';

/deep/ .el-dialog__header {
  background: url(../../assets/test.png);
}

.page {
  display: flex;
  font-family: 'SourceHanSansCN-Regular';
  height: 100%;
}

.table-box {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: scroll;
  box-sizing: border-box;
  background-color: #eff2f6;

  &::-webkit-scrollbar {
    display: none;
  }

  /*.history {
      font-weight: bold;
      font-size: 17px;

      & > div {
          line-height: 84px;

          span {
              color: gray;
          }
      }
  }*/

  .router-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    //overflow-y: auto;

    & > *:last-child {
      //flex-grow: 1 !important;
    }
  }
}
</style>
