<template>
    <div class="base-page">
        <!-- 列表 -->
        <div class="right">
            <div class="e-table topPDiv">
                <!-- -搜索栏----------------------------搜索栏 -->
                <div class="tabs">
                    <!-- <el-select v-model="filterData.evaluateType" @change="getReceiveList">
                        <el-option v-for="item in evaluateTypes" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select> -->
                    <!-- <el-radio-group v-model="filterData.evaluateType" @change="getReceiveList">
                        <el-radio border v-for="item in evaluateTypes" :key="item.value" :label="item.value">{{item.label}}</el-radio>
                    </el-radio-group> -->
                    <div class="tab df">
                        <div :class="!filterData.evaluateType ? 'active' : ''" @click="checkActiveTab(-1)">全部</div>
                        <div :class="filterData.evaluateType == 1 ? 'active' : ''" @click="checkActiveTab(1)">零星采购订单评价</div>
                        <div :class="filterData.evaluateType == 2 ? 'active' : ''" @click="checkActiveTab(2)">大宗临购订单评价</div>
                        <div :class="filterData.evaluateType == 3 ? 'active' : ''" @click="checkActiveTab(3)">周转材料订单评价</div>
                    </div>
                </div>
                <div class="top topDiv">
                    <!-- 新增按钮 -->
                    <div class="left">
                        <div>
                            <el-button class="btn-greenYellow" type="primary" @click="batch_operate(1)">显示</el-button>
                            <el-button class="btn-greenYellow" type="primary" @click="batch_operate(2)">隐藏</el-button>
                        </div>
                    </div>

                    <div class="search_box">
                        <el-input type="text" @keyup.enter.native="handleInputSearch" placeholder="输入搜索关键字" v-model="keywords">
                            <img src="@/assets/search.png" slot="suffix" @click="handleInputSearch" alt=""/>
                        </el-input>
                        <div class="adverse">
                            <el-button type="primary" size="small" @click="queryVisible = true">高级搜索</el-button>
                        </div>
                    </div>
                </div>
                <!-- -搜索栏----------------------------搜索栏 -->
            </div>
            <!-- ---------------------------------表格开始--------------------------------- -->
            <div class="e-table" :style="{ width: '100%' }">
                <el-table class="table" :height="rightTableHeight" :data="tableData" border highlight-current-row
                    @current-change="handleCurrentChange" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="40" fixed/>
                    <el-table-column label="操作" width="120" fixed>
                        <template slot-scope="scope">
                            <div class="action" v-if="scope.row.sfzd==1" @click="td_operate(scope, 2)">取消置顶</div>
                            <div class="action" v-else @click="td_operate(scope, 1)">置顶</div>
                            <div class="action" v-if="scope.row.state==1" @click="td_operate(scope, 4)">隐藏</div>
                            <div class="action" v-else @click="td_operate(scope, 3)">显示</div>
                        </template>
                    </el-table-column>
                    <!-- 订单编号 -->
                    <el-table-column label="订单编号" width="180" fixed>
                        <template slot-scope="scope">
                            <span class="action" @click="handleView(scope)">{{ scope.row.ddbh }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="评价内容" prop="pjnr" width="200"/>
                    <el-table-column label="用户昵称" prop="yhnc" width="120"/>
                    <el-table-column label="发布时间" prop="fbsj" width="150"/>
                    <el-table-column label="状态" width="100">
                        <template slot-scope="scope">
                            <span v-if="scope.row.state==1">显示</span>
                            <span v-else>隐藏</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否置顶" width="100">
                        <template slot-scope="scope">
                            <span v-if="scope.row.sfzd==1">是</span>
                            <span v-else>否</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品品质" prop="sppz" width="130"/>
                    <el-table-column label="保供能力" prop="gbnl" width="130"/>
                    <el-table-column label="诚信履约" prop="cxly" width="130"/>
                    <el-table-column label="服务水平" prop="fwsp" width="130"/>
                    <el-table-column label="类型" prop="pjlx" width="200"/>
                    <el-table-column label="店铺名称" prop="dpmc" width="200"/>

                </el-table>
            </div>
            <!-- 分页器 -->
            <ComPagination
                :total="pages.totalCount" :limit="20" :pageSize.sync="pages.pageSize"
                :currentPage.sync="pages.currPage"
                @currentChange="currentChange" @sizeChange="sizeChange"
            />
        </div>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog :close-on-click-modal="false" v-dialogDrag title="高级查询" :visible.sync="queryVisible" width="50%">
            <el-form :model="filterData" ref="form" label-width="120px" :inline="false" class="elForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品名称：">
                            <el-input v-model="filterData.spmc" placeholder="请输入商品名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="置顶状态：">
                            <el-select v-model="filterData.sfzd">
                                <el-option label="全部" :value="null"></el-option>
                                <el-option label="是" value="1"></el-option>
                                <el-option label="否" value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否隐藏：">
                            <el-select v-model="filterData.state">
                                <el-option label="全部" :value="null"></el-option>
                                <el-option label="是" value="0"></el-option>
                                <el-option label="否" value="1"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="商品品质：">
                            <el-select v-model="filterData.sppz">
                                <el-option v-for="item in evaluateLevels" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="保供能力：">
                            <el-select v-model="filterData.bgnl">
                                <el-option v-for="item in evaluateLevels" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="诚信履约：">
                            <el-select v-model="filterData.cxly">
                                <el-option v-for="item in evaluateLevels" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="服务水平：">
                            <el-select v-model="filterData.fwsp">
                                <el-option v-for="item in evaluateLevels" :key="item.value" :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                <!-- </el-row>
                <el-row> -->
                    <el-col :span="12">
                        <el-form-item label="发布日期：">
                            <el-date-picker
                                value-format="yyyy-MM-dd" v-model="filterData.dateValue" type="daterange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                            >
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button type="primary" @click="confirmSearch">查询</el-button>
                <el-button @click="resetSearchConditions">清空</el-button>
                <el-button @click="queryVisible = false">取消</el-button>
            </span>
        </el-dialog>
        <!-- ----------------查询弹框---------------- -->
        <el-dialog :close-on-click-modal="false" v-dialogDrag title="订单评价" :visible.sync="evaluationModalShow" width="50%">
            <el-form :model="evaluationModalForm" ref="evaForm" label-width="120px" :inline="false" class="elForm evaluationModalFormDiv">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="订单编号："><span class="textSpan textSpan1" :title="evaluationModalForm.ddbh">{{evaluationModalForm.ddbh}}</span></el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="用户昵称："><span class="textSpan textSpan1" :title="evaluationModalForm.yhnc">{{evaluationModalForm.yhnc}}</span></el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商铺名称："><span class="textSpan textSpan1" :title="evaluationModalForm.shopName">{{evaluationModalForm.shopName}}</span></el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="发布时间："><span class="textSpan">{{evaluationModalForm.fbsj}}</span></el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="机构："><span class="textSpan textSpan1" :title="evaluationModalForm.jgmc">{{evaluationModalForm.jgmc}}</span></el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="商品品质："><span class="textSpan">{{evaluationModalForm.sppz}}星</span></el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="保供能力："><span class="textSpan">{{evaluationModalForm.bgnl}}星</span></el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="诚信履约："><span class="textSpan">{{evaluationModalForm.cxly}}星</span></el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="服务水平："><span class="textSpan">{{evaluationModalForm.fwsp}}星</span></el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="评价内容："><span class="textSpan textSpan3">{{evaluationModalForm.pjnr}}</span></el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="评价图片："><span class="textSpan">
                            <img class="imgImg" v-for="(itemI, index) of evaluationModalForm.imgList" :key="index" :src="itemI.url"/>
                        </span></el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="tabs_title">商品明细评价</div>
            <div class="talbDiv" style="background-color: #fff">
                <el-table
                    border
                    style="width: 100%"
                    :data="evaluationModalForm.tableData"
                    :header-cell-style="{ background: '#f7f7f7', fontSize: '16px' }"
                    :row-style="{ fontSize: '14px', height: '48px' }"
                >
                    <el-table-column label="商品名称" prop="spmc" width="160"/>
                    <el-table-column label="评价内容" prop="pjnr" width=""/>
                    <el-table-column label="评价图片" prop="pjtp" width="200">
                        <template slot-scope="scope">
                            <img class="imgImg" v-for="(itemI, index) of scope.row.imgList" :key="index" :src="itemI.url"/>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import '@/utils/jquery.scrollTo.min'
import ComPagination from '@/components/pagination/pagination.vue'
import { debounce, hideLoading, showLoading, stripHtmlTags } from '@/utils/common'
import { mapActions } from 'vuex'

export default {
    components: {
        ComPagination,
        // editor
    },
    watch: {
        'filterData.orderBy': {
            handler () {
                this.getParams()
                this.getReceiveList()
            }
        }
    },
    computed: {
        // 左侧树高度
        leftTreeHeight () {
            return (this.screenHeight - 21) + 'px'
        },
        // 列表宽度
        rightTableWidth () {
            return (this.screenWidth - 302) + 'px'
        },
        // 列表高度
        rightTableHeight () {
            return this.screenHeight - 291 - 40
        },
    },
    data () {
        return {
            fileList: [],
            alertName: '消息',
            queryVisible: false,
            action: '编辑',
            keywords: '',
            currentRow: null,
            changedRow: [],
            selectedRows: [],
            pages: {
                currPage: 1,
                pageSize: 20,
            },
            // 高级查询数据对象
            filterData: {
                evaluateType: null,
                keywords: '',
                spmc: '',
                sfzd: null,
                state: null,
                sppz: null,
                bgnl: null,
                cxly: null,
                fwsp: null,
                dateValue: [], // 开始时间和结束时间
                orderBy: 2
            },
            tableData: [],
            evaluateTypes: [
                { value: null, label: '全部' },
                { value: 1, label: '零星采购订单评价' },
                { value: 2, label: '大宗临购订单评价' },
                { value: 3, label: '周转材料订单评价' }
            ],
            evaluateLevels: [
                { value: null, label: '全部' },
                { value: 1, label: '1星' },
                { value: 2, label: '2星' },
                { value: 3, label: '3星' },
                { value: 4, label: '4星' },
                { value: 5, label: '5星' }
            ],
            mapObj: null,
            screenWidth: 0,
            screenHeight: 0,
            arr: [],
            requestParams: {},

            evaluationModalForm: {
                ddbh: '', yhnc: '', shopName: '', fbsj: '', jgmc: '', sppz: '', bgnl: '', cxly: '', fwsp: '',
                pjnr: '', imgList: [], tableData: [],
            },
            evaluationModalShow: false,
        }
    },
    mounted () {
        // 获取屏幕宽高
        this.getScreenInfo()
        window.addEventListener('resize', debounce(this.getScreenInfo))
        // 获取计量单位
        this.setUnitMeasur()
    },
    created () {
        this.getParams()
        this.getReceiveList()
    },
    methods: {
        stripHtmlTags,
        checkActiveTab (type) {
            if(type != -1 && type != this.filterData.evaluateType) {
                this.filterData.evaluateType = type
                this.getReceiveList()
            }else {
                if(!this.filterData.evaluateType) {
                    this.filterData.evaluateType = null
                    this.getReceiveList()
                }else{
                    this.filterData.evaluateType = null
                }
            }
        },
        resetSearchConditions () {
            this.filterData.spmc = ''
            this.filterData.sfzd = null
            this.filterData.state = null
            this.filterData.sppz = null
            this.filterData.bgnl = null
            this.filterData.cxly = null
            this.filterData.fwsp = null
            this.filterData.dateValue = [] // 发布日期
        },
        handleInputSearch () {
            this.resetSearchConditions()
            this.getReceiveList()
        },
        confirmSearch () {
            this.keywords = ''
            this.getReceiveList()
            this.queryVisible = false
        },
        getReceiveList () {
            let params = {
                limit: this.pages.pageSize,
                page: this.pages.currPage,
                orderBy: this.filterData.orderBy,
                receiveType: 0, //用户类型 0 店铺  1 用户
            }
            if (!this.keywords) {
                params.keywords = this.keywords
            }
            if (this.filterData.evaluateType != null) {
                params.evaluateType = this.filterData.evaluateType
            }
            if (!this.filterData.spmc) {
                params.spmc = this.filterData.spmc
            }
            if (!this.filterData.sfzd) {
                params.sfzd = this.filterData.sfzd
            }
            if (!this.filterData.state) {
                params.state = this.filterData.state
            }
            if (!this.filterData.sppz) {
                params.sppz = this.filterData.sppz
            }
            if (!this.filterData.bgnl) {
                params.bgnl = this.filterData.bgnl
            }
            if (!this.filterData.cxly) {
                params.cxly = this.filterData.cxly
            }
            if (!this.filterData.fwsp) {
                params.fwsp = this.filterData.fwsp
            }
            if (this.filterData.dateValue != null) {
                params.startDate = this.filterData.dateValue[0]
                params.endDate = this.filterData.dateValue[1]
            }
            let res = {
                list: [
                    { ddbh: 'ddads11', sfzd: '1', state: '1', fbsj: '2025-01-11' },
                    { ddbh: 'ddads12', sfzd: '0', state: '0', fbsj: '2024-09-01' },
                    { ddbh: 'ddads13', sfzd: '1', state: '0', fbsj: '2024-01-21' },
                    { ddbh: 'ddads14', sfzd: '0', state: '1', fbsj: '2023-11-24' }
                ], currPage: 1, pageSize: 10, totalCount: 4, totalPage: 1
            }
            this.pages.currPage = res.currPage
            this.pages.totalPage = res.totalPage
            this.pages.pageSize = res.pageSize
            this.pages.total = res.totalCount
            this.tableData = res.list
        },
        // 获取页面参数
        getParams () {
            this.requestParams = {
                keywords: this.keywords,
                limit: this.pages.pageSize,
                ...this.filterData
            }
            this.requestParams.page = this.pages.currPage
        },
        // 删除
        td_operate (scope, type) {
            let id  = scope.row.id
            console.log(id)
            if (type === 1) {
                this.clientPop('info', '您确定要置顶该评价吗？', async () => {
                    showLoading()
                    let res = { message: '操作成功' }
                    if (res.message === '操作成功') {
                        this.$message.success('操作成功')
                        this.getReceiveList()
                    }
                    hideLoading()
                })
            } else if (type === 2) {
                this.clientPop('info', '您确定要取消置顶该评价吗？', async () => {
                    showLoading()
                    let res = { message: '操作成功' }
                    if (res.message === '操作成功') {
                        this.$message.success('操作成功')
                        this.getReceiveList()
                    }
                    hideLoading()
                })
            } else if (type === 3) {
                this.clientPop('info', '您确定要显示该评价吗？', async () => {
                    showLoading()
                    let res = { message: '操作成功' }
                    if (res.message === '操作成功') {
                        this.$message.success('操作成功')
                        this.getReceiveList()
                    }
                    hideLoading()
                })
            } else if (type === 4) {
                this.clientPop('info', '您确定要隐藏该评价吗？', async () => {
                    showLoading()
                    let res = { message: '操作成功' }
                    if (res.message === '操作成功') {
                        this.$message.success('操作成功')
                        this.getReceiveList()
                    }
                    hideLoading()
                })
            }
        },
        // 批量显示、隐藏
        batch_operate (type) {
            if (!this.selectedRows[0]) {
                return this.clientPop('warn', '请选择要操作的信息', () => {})
            }
            let arr = this.selectedRows.map(item => {
                return item.ddbh
            })
            console.log(arr)
            if( type == 1 ) {
                this.clientPop('info', '您确定要显示选中评价吗？', async () => {
                    showLoading()
                    let res = { message: '操作成功' }
                    if (res.message === '操作成功') {
                        this.$message.success('操作成功')
                        this.getReceiveList()
                    } else {
                        this.$message.warn('操作失败请稍后再试')
                    }
                    hideLoading()
                })
            }else if (type == 2) {
                this.clientPop('info', '您确定要隐藏选中评价吗？', async () => {
                    showLoading()
                    let res = { message: '操作失败' }
                    if (res.message === '操作成功') {
                        this.$message.success('操作成功')
                        this.getReceiveList()
                    } else {
                        this.$message.warning('操作失败请稍后再试')
                    }
                    hideLoading()
                })
            }
        },
        // 表格勾选
        handleSelectionChange (selection) {
            this.selectedRows = selection
        },
        // 分页函数
        currentChange () {
            if (this.pages.currPage === undefined) {
                this.pages.currPage = this.pages.totalPage
            }
            this.getTableData()
        },
        sizeChange () {
            this.getTableData()
        },
        ...mapActions('equip', ['setUnitMeasur']),
        handleView (scopeObj) {
            let ddbh = scopeObj.row.ddbh
            console.log(ddbh)
            let res = {
                ddbh: 'fdafasfa', yhnc: 'ffdsafa', shopName: 'fdsafa', fbsj: '2022-11-22', jgmc: '机构名称',
                sppz: '1', bgnl: '2', cxly: '3', fwsp: '4',
                pjnr: '评价内容', imgList: [
                    { url: 'fdafadfa' }
                ], tableData: [
                    { spmc: '商品名称12', pjnr: '评价内容21', imgList: [
                        { url: 'fdafadfa' }
                    ] }, { spmc: '商品名称13', pjnr: '评价内容22', imgList: [
                        { url: 'fdafadfa' }, { url: 'fdafadfa' }
                    ] }, { spmc: '商品名称14', pjnr: '评价内容23', imgList: [
                        { url: 'fdafadfa' }
                    ] }
                ],
            }
            this.evaluationModalForm = res
            this.evaluationModalShow = true
        },
        // 获取列表数据
        async getTableData () {
            this.getParams()
            this.getReceiveList()
        },
        // 关键词搜索
        handleCurrentChange (val) {
            this.currentRow = val
        },
        // 获取屏幕大小
        getScreenInfo () {
            let screenWidth = document.documentElement.clientWidth || document.body.clientWidth
            let screenHeight = document.documentElement.clientHeight || document.body.clientHeight
            this.screenWidth = screenWidth
            this.screenHeight = screenHeight
        }
    }
}
</script>

<style lang="scss" scoped>
.base-page .left {
    min-width: 180px;
    padding: 0;
}

.base-page {
    width: 100%;
    height: 100%;
}

/deep/ .e-form .el-form-item .el-form-item__content {
    display: flex;
    align-items: center;
}

.detail-info {
    width: 100%;
    // min-width: 670px;
    padding: 30px 20px 70px;
    background-color: #fff;
    border-radius: 14px;
    box-shadow: 1px 1px 5px rgba(136, 136, 136, 0.5);
    position: relative;

    .buttons {
        right: 30px;
        bottom: 20px;
        position: absolute;
        text-align: right;
    }
}

.e-table {
    min-height: auto;
}

.separ {
    display: inline-block;
    font-weight: bold;
    width: 4%;
    margin: 0 1% 0 1%;
    text-align: center;
}

/deep/ .el-input--suffix .el-input__inner {
    padding-right: 5px;
    border-radius: 5px;
}

.action {
    margin-right: 10px;
    color: #2e61d7;
    cursor: pointer;
}

/deep/ .el-table td.el-table__cell {
    // height: 150px !important; // 单独修改表格行高度
}

/deep/ .el-col.editorCol {
    .el-form-item__content {
        height: unset !important;

        .content {
            width: 100%;
            height: 300px;
            overflow: auto;
            border: 1px solid lightgray
        }
    }
}

.receiveText {

    font-size: 15px;
    width: 1115px;
    height: 304px;
    border: solid 1px #cbc3c3;
    margin-right: 30px;
    padding: 20px
}
.left-btn {
    margin-left: 30px;
    /deep/ .el-radio {margin-right: 0;}
}
.elForm /deep/ .el-select, .elForm /deep/ .el-date-editor--daterange.el-input__inner {width: 100%;}
/deep/ .el-dialog .el-dialog__body {margin: 30px 0 10px;height: auto;}

.topPDiv {
    position: relative;
    .topDiv {padding-top: 40px;height: 95px;}
    .tabs {position: absolute;top: 20px;left: 20px;}
    .tab {
        font-size: 16px;color: #666;
        div {margin-right: 20px;cursor: pointer;min-width: 60px;text-align: center;}
        .active {
            color: #000;
            &::after {
                content: '';
                display: block;
                width: 100%;
                height: 2px;
                margin-top: 4px;
                background-color: #226fc7;
            }
        }
    }
}
.textSpan {
    display: block;width: 100%;color: #000;
    .imgImg {width: 100px;max-height: 200px;margin-right: 10px;}
}
.textSpan1 {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.textSpan3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tabs_title {color: #000;font-size: 16px;font-weight: bold;margin: 10px 0;}
.talbDiv .imgImg {max-width: 100px;height: 60px;margin-right: 5px;}
.evaluationModalFormDiv /deep/ .el-form-item--small.el-form-item {margin-bottom: 8px;}
</style>
